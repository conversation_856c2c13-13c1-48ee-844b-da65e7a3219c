#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复IPSec配置冲突
"""

import boto3
import json
import time

def send_command(ssm, instance_id, commands, description):
    """发送单个命令"""
    try:
        print(f"\n🔧 {description}")
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={'commands': commands},
            TimeoutSeconds=120
        )
        
        command_id = response['Command']['CommandId']
        
        # 等待命令完成
        for i in range(30):
            time.sleep(4)
            try:
                result = ssm.get_command_invocation(
                    CommandId=command_id,
                    InstanceId=instance_id
                )
                
                status = result['Status']
                if status == 'Success':
                    output = result['StandardOutputContent']
                    if output.strip():
                        print(f"✅ 成功: {output[-600:]}")
                    else:
                        print("✅ 成功")
                    return True
                elif status == 'Failed':
                    error = result['StandardErrorContent']
                    print(f"❌ 失败: {error}")
                    return False
                elif status in ['InProgress', 'Pending']:
                    continue
                    
            except Exception as e:
                if 'InvocationDoesNotExist' in str(e):
                    continue
                else:
                    print(f"❌ 错误: {e}")
                    return False
        
        print("⚠️ 超时")
        return False
        
    except Exception as e:
        print(f"❌ 发送命令失败: {e}")
        return False

def main():
    try:
        # 读取部署信息
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        instance_id = deployment_info['instance_id']
        region = deployment_info['region']
        
        print(f"实例ID: {instance_id}")
        
        # 创建SSM客户端
        ssm = boto3.client('ssm', region_name=region)
        
        # 步骤1: 停止IPSec服务
        send_command(ssm, instance_id, [
            'systemctl stop ipsec',
            'systemctl disable ipsec'
        ], "停止IPSec服务")
        
        # 步骤2: 清理旧配置
        send_command(ssm, instance_id, [
            'rm -f /etc/ipsec.conf',
            'rm -f /etc/ipsec.secrets',
            'rm -rf /etc/ipsec.d/policies/*',
            'rm -f /var/lib/ipsec/nss/*'
        ], "清理旧配置文件")
        
        # 步骤3: 获取公网IP
        send_command(ssm, instance_id, [
            'TOKEN=$(curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")',
            'PUBLIC_IP=$(curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/public-ipv4)',
            'echo "公网IP: $PUBLIC_IP"',
            'echo $PUBLIC_IP > /tmp/public_ip'
        ], "获取公网IP")
        
        # 步骤4: 创建简单的IPSec配置
        send_command(ssm, instance_id, [
            'PUBLIC_IP=$(cat /tmp/public_ip)',
            'cat > /etc/ipsec.conf << EOF',
            'version 2.0',
            '',
            'config setup',
            '    nat_traversal=yes',
            '    virtual_private=%v4:10.0.0.0/8,%v4:***********/16,%v4:**********/12',
            '    oe=off',
            '    protostack=netkey',
            '    nhelpers=0',
            '    interfaces=%defaultroute',
            '',
            'conn vpn-ikev2',
            '    auto=add',
            '    compress=no',
            '    type=tunnel',
            '    keyexchange=ikev2',
            '    fragmentation=yes',
            '    forceencaps=yes',
            '    dpdaction=clear',
            '    dpddelay=300s',
            '    rekey=no',
            '    left=%defaultroute',
            '    leftid=$PUBLIC_IP',
            '    leftauth=psk',
            '    leftsubnet=0.0.0.0/0',
            '    right=%any',
            '    rightid=%any',
            '    rightauth=psk',
            '    rightsourceip=**********/24',
            '    rightdns=*******,*******',
            'EOF'
        ], "创建新的IPSec配置")
        
        # 步骤5: 创建密钥文件
        send_command(ssm, instance_id, [
            'PUBLIC_IP=$(cat /tmp/public_ip)',
            'cat > /etc/ipsec.secrets << EOF',
            ': PSK "GoogleVPN2023!"',
            '$PUBLIC_IP %any : PSK "GoogleVPN2023!"',
            'EOF',
            'chmod 600 /etc/ipsec.secrets'
        ], "创建密钥文件")
        
        # 步骤6: 验证配置
        send_command(ssm, instance_id, [
            'ipsec addconn --config /etc/ipsec.conf --checkconfig'
        ], "验证配置文件")
        
        # 步骤7: 启动IPSec服务
        send_command(ssm, instance_id, [
            'systemctl enable ipsec',
            'systemctl start ipsec'
        ], "启动IPSec服务")
        
        # 步骤8: 检查服务状态
        send_command(ssm, instance_id, [
            'systemctl status ipsec --no-pager',
            'ss -unp | grep -E ":(500|4500)"'
        ], "检查服务状态")
        
        # 步骤9: 检查连接状态
        send_command(ssm, instance_id, [
            'ipsec status',
            'ipsec whack --status'
        ], "检查连接状态")
        
        print("\n✅ IPSec配置修复完成！")
        print("现在可以尝试VPN连接了")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    print("=== IPSec配置修复工具 ===")
    main()
