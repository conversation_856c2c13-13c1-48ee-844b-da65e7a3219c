#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复VPN服务
"""

import boto3
import json
import time

def run_fix_commands():
    """运行VPN服务修复命令"""
    try:
        # 读取部署信息
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        instance_id = deployment_info['instance_id']
        region = deployment_info['region']
        
        print(f"实例ID: {instance_id}")
        print(f"地区: {region}")
        
        # 创建SSM客户端
        ssm = boto3.client('ssm', region_name=region)
        
        # 修复命令
        print("\n🔧 开始修复VPN服务...")
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={
                'commands': [
                    'echo "=== 安装VPN软件包 ==="',
                    'yum update -y',
                    'yum install -y libreswan xl2tpd',
                    'echo "=== 配置系统参数 ==="',
                    'echo "net.ipv4.ip_forward = 1" >> /etc/sysctl.conf',
                    'echo "net.ipv4.conf.all.accept_redirects = 0" >> /etc/sysctl.conf',
                    'echo "net.ipv4.conf.all.send_redirects = 0" >> /etc/sysctl.conf',
                    'sysctl -p',
                    'echo "=== 获取公网IP ==="',
                    'PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)',
                    'echo "公网IP: $PUBLIC_IP"',
                    'echo "=== 配置IPSec ==="',
                    'cat > /etc/ipsec.conf << EOF',
                    'version 2.0',
                    'config setup',
                    '    nat_traversal=yes',
                    '    virtual_private=%v4:10.0.0.0/8,%v4:***********/16,%v4:**********/12',
                    '    oe=off',
                    '    protostack=netkey',
                    '    nhelpers=0',
                    '    interfaces=%defaultroute',
                    '',
                    'conn vpnpsk',
                    '    connaddrfamily=ipv4',
                    '    auto=add',
                    '    left=%defaultroute',
                    '    leftid=$PUBLIC_IP',
                    '    leftsubnet=0.0.0.0/0',
                    '    leftprotoport=17/1701',
                    '    rightprotoport=17/%any',
                    '    right=%any',
                    '    rightsubnetwithin=0.0.0.0/0',
                    '    forceencaps=yes',
                    '    authby=secret',
                    '    pfs=no',
                    '    type=transport',
                    '    auth=esp',
                    '    ike=3des-sha1,aes-sha1',
                    '    phase2alg=3des-sha1,aes-sha1',
                    '    dpddelay=40',
                    '    dpdtimeout=130',
                    '    dpdaction=clear',
                    'EOF',
                    'echo "=== 配置IPSec密钥 ==="',
                    'cat > /etc/ipsec.secrets << EOF',
                    '$PUBLIC_IP %any : PSK "GoogleVPN2023!"',
                    'EOF',
                    'echo "=== 配置L2TP ==="',
                    'cat > /etc/xl2tpd/xl2tpd.conf << EOF',
                    '[global]',
                    'port = 1701',
                    '',
                    '[lns default]',
                    'ip range = ***********-************',
                    'local ip = **********',
                    'require chap = yes',
                    'refuse pap = yes',
                    'require authentication = yes',
                    'name = l2tpd',
                    'ppp debug = yes',
                    'pppoptfile = /etc/ppp/options.xl2tpd',
                    'length bit = yes',
                    'EOF',
                    'echo "=== 配置PPP ==="',
                    'cat > /etc/ppp/options.xl2tpd << EOF',
                    'ipcp-accept-local',
                    'ipcp-accept-remote',
                    'ms-dns *******',
                    'ms-dns *******',
                    'noccp',
                    'auth',
                    'crtscts',
                    'idle 1800',
                    'mtu 1280',
                    'mru 1280',
                    'lock',
                    'connect-delay 5000',
                    'EOF',
                    'echo "=== 配置用户认证 ==="',
                    'cat > /etc/ppp/chap-secrets << EOF',
                    'vpnuser l2tpd GoogleVPN2023! *',
                    'EOF',
                    'echo "=== 启动服务 ==="',
                    'systemctl enable ipsec',
                    'systemctl enable xl2tpd',
                    'systemctl restart ipsec',
                    'systemctl restart xl2tpd',
                    'echo "=== 检查服务状态 ==="',
                    'systemctl status ipsec --no-pager',
                    'systemctl status xl2tpd --no-pager',
                    'echo "=== 检查监听端口 ==="',
                    'ss -unp | grep -E ":(500|1701|4500)"',
                    'echo "=== 配置防火墙 ==="',
                    'iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE',
                    'iptables -A INPUT -p udp --dport 500 -j ACCEPT',
                    'iptables -A INPUT -p udp --dport 4500 -j ACCEPT',
                    'iptables -A INPUT -p udp --dport 1701 -j ACCEPT',
                    'iptables -A FORWARD -s **********/24 -j ACCEPT',
                    'iptables -A FORWARD -d **********/24 -j ACCEPT',
                    'echo "=== 保存防火墙规则 ==="',
                    'service iptables save || echo "iptables save not available"',
                    'echo "=== VPN配置完成 ==="'
                ]
            },
            TimeoutSeconds=300  # 5分钟超时
        )
        
        command_id = response['Command']['CommandId']
        print(f"命令ID: {command_id}")
        
        # 等待命令完成
        print("等待命令执行...")
        for i in range(60):  # 等待最多60秒
            time.sleep(5)
            try:
                result = ssm.get_command_invocation(
                    CommandId=command_id,
                    InstanceId=instance_id
                )
                
                status = result['Status']
                print(f"状态: {status} ({i*5}秒)")
                
                if status == 'Success':
                    print("\n✅ VPN服务修复成功！")
                    print("=== 输出结果 ===")
                    output = result['StandardOutputContent']
                    if len(output) > 2000:
                        print(output[-2000:])  # 只显示最后2000字符
                    else:
                        print(output)
                    if result['StandardErrorContent']:
                        print("=== 错误输出 ===")
                        print(result['StandardErrorContent'])
                    return True
                elif status == 'Failed':
                    print("\n❌ VPN服务修复失败！")
                    print("=== 错误输出 ===")
                    print(result['StandardErrorContent'])
                    return False
                elif status in ['InProgress', 'Pending']:
                    continue
                else:
                    print(f"未知状态: {status}")
                    return False
                    
            except Exception as e:
                if 'InvocationDoesNotExist' in str(e):
                    continue
                else:
                    print(f"获取命令结果失败: {e}")
                    return False
        
        print("⚠️ 命令执行超时")
        return False
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

if __name__ == "__main__":
    print("=== VPN服务修复工具 ===")
    run_fix_commands()
