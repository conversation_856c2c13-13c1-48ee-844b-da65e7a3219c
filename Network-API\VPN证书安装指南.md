# VPN证书安装指南

## 证书文件说明

本项目提供以下证书文件：

- `ca.crt` - CA根证书 (所有客户端都需要)
- `client.crt` - 客户端证书 (Mac/Linux使用)
- `client.p12` - PKCS#12格式证书 (Windows推荐)
- 密码: `GoogleVPN2023!`

## Windows 证书安装

### 方法1: 使用PKCS#12证书 (推荐)

1. **安装证书**
   - 双击 `client.p12` 文件
   - 选择"当前用户"
   - 输入密码: `GoogleVPN2023!`
   - 选择"自动选择证书存储区"
   - 完成安装

2. **配置VPN连接**
   - 设置 → 网络和Internet → VPN → 添加VPN连接
   - VPN提供商: Windows (内置)
   - 连接名称: Google VPN 证书
   - 服务器: *************
   - VPN类型: IKEv2
   - 登录信息类型: 证书
   - 选择刚安装的证书

### 方法2: 手动安装证书

1. **安装CA证书**
   - 右键 `ca.crt` → 安装证书
   - 存储位置: 本地计算机
   - 证书存储: 受信任的根证书颁发机构

2. **安装客户端证书**
   - 右键 `client.crt` → 安装证书
   - 存储位置: 当前用户
   - 证书存储: 个人

## Mac 证书安装

### 安装步骤

1. **安装CA证书**
   - 双击 `ca.crt` 文件
   - 添加到"钥匙串访问"
   - 选择"系统"钥匙串
   - 找到证书，双击设置信任为"始终信任"

2. **安装客户端证书**
   - 双击 `client.crt` 文件
   - 添加到"登录"钥匙串

3. **配置VPN连接**
   - 系统偏好设置 → 网络
   - 点击"+" → VPN → IKEv2
   - 服务器地址: *************
   - 远程ID: *************
   - 认证设置: 证书
   - 选择客户端证书

## iOS 证书安装

### 安装步骤

1. **发送证书到设备**
   - 将 `ca.crt` 和 `client.crt` 通过邮件发送到iOS设备
   - 或使用AirDrop传输

2. **安装证书**
   - 点击邮件中的证书附件
   - 设置 → 通用 → VPN与设备管理 → 配置描述文件
   - 安装证书

3. **信任证书**
   - 设置 → 通用 → 关于本机 → 证书信任设置
   - 启用对根证书的完全信任

4. **配置VPN**
   - 设置 → 通用 → VPN → 添加VPN配置
   - 类型: IKEv2
   - 服务器: *************
   - 远程ID: *************
   - 用户认证: 证书
   - 选择客户端证书

## Android 证书安装

### 安装步骤

1. **安装CA证书**
   - 设置 → 安全 → 加密与凭据 → 从存储设备安装
   - 选择 `ca.crt` 文件
   - 证书用途: VPN和应用

2. **安装客户端证书**
   - 将 `client.crt` 和私钥合并为PKCS#12格式
   - 或使用第三方VPN客户端

3. **配置VPN**
   - 设置 → 网络和互联网 → VPN
   - 添加VPN配置
   - 类型: IKEv2/IPSec RSA
   - 服务器地址: *************
   - 用户证书: 选择安装的证书

## 证书认证优势

### 相比密码认证的优势

1. **更高安全性**
   - 基于公钥加密，无法被暴力破解
   - 证书有有效期，可定期更换

2. **无需记忆密码**
   - 证书自动认证，用户体验更好
   - 避免密码泄露风险

3. **企业级管理**
   - 可批量生成和管理证书
   - 支持证书撤销列表(CRL)

### Windows用户特别说明

- Windows 10/11 原生支持IKEv2证书认证
- 推荐使用PKCS#12格式证书
- 证书安装后无需输入用户名密码
- 支持自动重连和开机自启

## 故障排除

### 常见问题

1. **证书安装失败**
   - 检查证书文件是否损坏
   - 确认密码输入正确
   - 以管理员身份运行

2. **VPN连接失败**
   - 检查服务器地址是否正确
   - 确认证书已正确安装
   - 检查防火墙设置

3. **证书不被信任**
   - 确保CA证书已安装到正确位置
   - 检查证书信任设置
   - 重启设备后重试

### 技术支持

如遇到问题，请检查：
- 证书有效期 (1年)
- 服务器时间同步
- 网络连通性
- 防火墙配置

## 证书更新

证书有效期为1年，到期前需要：
1. 重新生成证书
2. 分发新证书给用户
3. 更新服务器配置
4. 用户重新安装证书
