import boto3
import json
import configparser
import logging
import time
import os
from botocore.exceptions import ClientError

# --- Configuration ---
LOGGING_LEVEL = logging.INFO
CONFIG_FILE = 'vpn_config.ini'
DEPLOYMENT_INFO_FILE = 'vpn_deployment_info.json'

# --- Setup Logging ---
logging.basicConfig(level=LOGGING_LEVEL, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_vpn_setup_script(psk):
    """
    Generates the corrected VPN server setup script.
    This script dynamically fetches the instance's public IP.
    """
    script = f'''#!/bin/bash
exec > /var/log/vpn-setup-fix.log 2>&1
echo "--- Starting VPN Server Configuration Fix ---"
date

# Step 1: Dynamically get the instance's public IP from EC2 metadata
echo "Fetching public IP from EC2 metadata..."
TOKEN=$(curl -s -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")
PUBLIC_IP=$(curl -s -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/public-ipv4)
if [ -z "$PUBLIC_IP" ]; then
    echo "FATAL: Could not retrieve public IP. Exiting."
    exit 1
fi
echo "Successfully fetched public IP: $PUBLIC_IP"

# Step 2: Update and install necessary packages
echo "Updating packages and installing VPN software..."
yum update -y
amazon-linux-extras install -y epel
yum install -y strongswan xl2tpd ppp net-tools

# Step 3: Configure kernel parameters for IP forwarding
echo "Configuring IP forwarding..."
cat > /etc/sysctl.conf << 'SYSCTL_EOF'
net.ipv4.ip_forward = 1
net.ipv6.conf.all.forwarding = 1
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.all.send_redirects = 0
SYSCTL_EOF
sysctl -p

# Step 4: Configure strongSwan (ipsec.conf) with the dynamic IP
echo "Configuring strongSwan (ipsec.conf)..."
cat > /etc/strongswan/ipsec.conf << IPSEC_EOF
config setup
    charondebug="ike 2, knl 2, cfg 2"
    uniqueids=no

conn %default
    left=%defaultroute
    leftsubnet=0.0.0.0/0
    right=%any
    rightsourceip=**********/24
    rightdns=*******,*******
    auto=add
    keyexchange=ikev2
    ike=aes256-sha256-modp2048!
    esp=aes256-sha256!
    dpdaction=clear
    dpddelay=300s
    rekey=no
    leftfirewall=yes
    
conn IKEV2-PSK-EAP
    leftid=$PUBLIC_IP
    rightauth=eap-mschapv2
    eap_identity=%identity
    also=%default
IPSEC_EOF

# Step 5: Configure strongSwan secrets (ipsec.secrets) with the dynamic IP
echo "Configuring strongSwan secrets (ipsec.secrets)..."
cat > /etc/strongswan/ipsec.secrets << SECRETS_EOF
$PUBLIC_IP %any : PSK "{psk}"
vpnuser : EAP "{psk}"
testuser : EAP "{psk}"
admin : EAP "{psk}"
SECRETS_EOF

# Step 6: Configure L2TP (xl2tpd.conf and ppp options)
echo "Configuring L2TP..."
cat > /etc/xl2tpd/xl2tpd.conf << L2TP_EOF
[lns default]
ip range = ***********-************
local ip = **********
require chap = yes
refuse pap = yes
require authentication = yes
name = l2tpd
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
L2TP_EOF

cat > /etc/ppp/options.xl2tpd << PPP_EOF
ipcp-accept-local
ipcp-accept-remote
ms-dns *******
ms-dns *******
noccp
auth
crtscts
idle 1800
mtu 1410
mru 1410
nodefaultroute
debug
lock
proxyarp
connect-delay 5000
require-mschap-v2
PPP_EOF

cat > /etc/ppp/chap-secrets << CHAP_EOF
vpnuser l2tpd "{psk}" *
testuser l2tpd "{psk}" *
admin l2tpd "{psk}" *
CHAP_EOF

# Step 7: Configure Firewall (iptables) for NAT
echo "Configuring firewall and NAT rules..."
iptables -t nat -F POSTROUTING
iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE
iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE
iptables-save > /etc/iptables.rules

# Step 8: Restart VPN services to apply changes
echo "Restarting VPN services..."
systemctl restart strongswan
systemctl restart xl2tpd

echo "--- VPN Server Configuration Fix Completed ---"
date
'''
    return script

def main():
    """Main function to execute the fix."""
    logger.info("Starting VPN Server Configuration Fixer...")

    # 1. Load deployment info
    if not os.path.exists(DEPLOYMENT_INFO_FILE):
        logger.error(f"Deployment info file '{DEPLOYMENT_INFO_FILE}' not found. Cannot proceed.")
        return
    with open(DEPLOYMENT_INFO_FILE, 'r') as f:
        deployment_info = json.load(f)
    
    instance_id = deployment_info.get('instance_id')
    region = deployment_info.get('region')
    if not instance_id or not region:
        logger.error("Instance ID or region not found in deployment info file.")
        return
        
    logger.info(f"Targeting instance '{instance_id}' in region '{region}'.")

    # 2. Load Pre-Shared Key from config
    config = configparser.ConfigParser()
    if not os.path.exists(CONFIG_FILE):
        logger.error(f"Config file '{CONFIG_FILE}' not found. Cannot get PSK.")
        return
    config.read(CONFIG_FILE)
    psk = config.get('VPN', 'PreSharedKey', fallback=None)
    if not psk:
        logger.error("PreSharedKey not found in vpn_config.ini.")
        return
        
    logger.info("Successfully loaded Pre-Shared Key.")

    # 3. Generate the fix script
    fix_script = get_vpn_setup_script(psk)
    
    # 4. Execute the script on the instance using AWS SSM Run Command
    logger.info("Connecting to AWS SSM to send the fix command...")
    try:
        ssm_client = boto3.client('ssm', region_name=region)
        
        response = ssm_client.send_command(
            InstanceIds=[instance_id],
            DocumentName='AWS-RunShellScript',
            Parameters={'commands': [fix_script]},
            TimeoutSeconds=600,
            Comment='Automated VPN configuration fix to correct the server IP (leftid).'
        )
        
        command_id = response['Command']['CommandId']
        logger.info(f"SSM command sent successfully. Command ID: {command_id}")
        
        # 5. Wait for the command to complete and check the result
        logger.info("Waiting for command to complete... This may take a few minutes.")
        time.sleep(10) # Initial wait
        
        waiter = ssm_client.get_waiter('command_executed')
        try:
            waiter.wait(
                CommandId=command_id,
                InstanceId=instance_id,
                WaiterConfig={'Delay': 15, 'MaxAttempts': 20}
            )
            logger.info("SSM command execution has completed.")
        except Exception as e:
            logger.error(f"Waiter failed: {e}")
            # Continue to try and get output anyway

        output_response = ssm_client.get_command_invocation(
            CommandId=command_id,
            InstanceId=instance_id,
        )
        
        status = output_response.get('Status')
        stdout = output_response.get('StandardOutputContent', 'No stdout captured.')
        stderr = output_response.get('StandardErrorContent', 'No stderr captured.')

        logger.info(f"Command status: {status}")
        print("\n--- Server Execution Log (stdout) ---")
        print(stdout)
        print("------------------------------------")
        
        if stderr:
            print("\n--- Server Execution Log (stderr) ---")
            print(stderr)
            print("------------------------------------")

        if status == 'Success':
            logger.info("✅✅✅ VPN server configuration fix applied successfully!")
        else:
            logger.error("❌❌❌ VPN server configuration fix failed. Check the logs above.")

    except ClientError as e:
        if "TargetNotConnected" in str(e):
            logger.error("SSM Error: The target instance is not connected to SSM.")
            logger.error("Please ensure the SSM Agent is running on the instance and it has the correct IAM role (e.g., AmazonSSMManagedInstanceCore).")
        else:
            logger.error(f"An AWS error occurred: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    main()
