#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API功能测试脚本 - 验证VPN API的核心功能
不依赖Flask，直接测试业务逻辑
"""

import json
import os
import socket
import subprocess
import boto3
from datetime import datetime

def load_deployment_info():
    """加载部署信息"""
    try:
        with open('vpn_deployment_info.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return None

def load_vpn_config():
    """加载VPN配置"""
    try:
        import configparser
        config = configparser.ConfigParser()
        config.read('vpn_config.ini')
        return config
    except:
        return None

def test_port(ip, port, protocol='udp'):
    """测试端口连通性"""
    try:
        if protocol == 'tcp':
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        else:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3)
        result = sock.connect_ex((ip, port))
        sock.close()
        return result == 0
    except:
        return False

def test_vpn_status():
    """测试VPN状态检查功能"""
    print("=== 测试VPN状态检查功能 ===")
    
    deployment_info = load_deployment_info()
    if not deployment_info:
        print("❌ VPN服务器未部署")
        return False
    
    try:
        # 检查AWS实例状态
        ec2 = boto3.client('ec2', region_name=deployment_info['region'])
        response = ec2.describe_instances(InstanceIds=[deployment_info['instance_id']])
        instance = response['Reservations'][0]['Instances'][0]
        
        print(f"✅ 实例状态: {instance['State']['Name']}")
        print(f"✅ 服务器IP: {deployment_info['public_ip']}")
        
        # 检查端口连通性
        ports_status = {
            'ikev2': test_port(deployment_info['public_ip'], 500),
            'l2tp': test_port(deployment_info['public_ip'], 1701),
            'nat_t': test_port(deployment_info['public_ip'], 4500)
        }
        
        print(f"端口状态:")
        for protocol, status in ports_status.items():
            print(f"  {protocol}: {'✅ 可达' if status else '❌ 不可达'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 状态检查失败: {e}")
        return False

def test_vpn_config():
    """测试VPN配置获取功能"""
    print("\n=== 测试VPN配置获取功能 ===")
    
    deployment_info = load_deployment_info()
    vpn_config = load_vpn_config()
    
    if not deployment_info:
        print("❌ VPN服务器未部署")
        return False
    
    config_data = {
        'server_ip': deployment_info['public_ip'],
        'psk': 'GoogleVPN2023!',
        'users': {
            'vpnuser': 'GoogleVPN2023!',
            'testuser': 'GoogleVPN2023!',
            'admin': 'GoogleVPN2023!'
        },
        'protocols': {
            'ikev2': {
                'port': 500,
                'nat_port': 4500,
                'client_subnet': '**********/24'
            },
            'l2tp': {
                'port': 1701,
                'client_subnet': '**********/24'
            }
        },
        'dns': ['*******', '*******']
    }
    
    print("✅ VPN配置信息:")
    print(f"  服务器IP: {config_data['server_ip']}")
    print(f"  预共享密钥: {config_data['psk']}")
    print(f"  用户数量: {len(config_data['users'])}")
    print(f"  支持协议: {list(config_data['protocols'].keys())}")
    print(f"  DNS服务器: {', '.join(config_data['dns'])}")
    
    return True

def test_connection_test():
    """测试连接测试功能"""
    print("\n=== 测试连接测试功能 ===")
    
    deployment_info = load_deployment_info()
    if not deployment_info:
        print("❌ VPN服务器未部署")
        return False
    
    try:
        # 基础端口测试
        results = {
            'ikev2_port': test_port(deployment_info['public_ip'], 500),
            'nat_t_port': test_port(deployment_info['public_ip'], 4500),
            'l2tp_port': test_port(deployment_info['public_ip'], 1701)
        }
        
        print("✅ 连接测试结果:")
        print(f"  IKEv2端口(500): {'✅ 可达' if results['ikev2_port'] else '❌ 不可达'}")
        print(f"  NAT-T端口(4500): {'✅ 可达' if results['nat_t_port'] else '❌ 不可达'}")
        print(f"  L2TP端口(1701): {'✅ 可达' if results['l2tp_port'] else '❌ 不可达'}")
        print(f"  总体状态: {'✅ 正常' if all(results.values()) else '❌ 异常'}")
        
        return all(results.values())
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_system_info():
    """测试系统信息功能"""
    print("\n=== 测试系统信息功能 ===")
    
    system_info = {
        'version': '1.0.0',
        'name': 'Network-API VPN Server',
        'description': '支持IKEv2和L2TP协议的VPN服务器',
        'supported_platforms': ['Windows', 'macOS', 'iOS', 'Android'],
        'api_version': 'v1',
        'timestamp': datetime.now().isoformat()
    }
    
    print("✅ 系统信息:")
    print(f"  名称: {system_info['name']}")
    print(f"  版本: {system_info['version']}")
    print(f"  API版本: {system_info['api_version']}")
    print(f"  支持平台: {', '.join(system_info['supported_platforms'])}")
    print(f"  时间戳: {system_info['timestamp']}")
    
    return True

def test_browser_extension_integration():
    """测试浏览器扩展集成"""
    print("\n=== 测试浏览器扩展集成 ===")
    
    # 检查浏览器扩展示例文件
    if os.path.exists('browser_extension_example.html'):
        print("✅ 浏览器扩展示例文件存在")
        
        # 检查文件内容
        with open('browser_extension_example.html', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'API_BASE' in content:
            print("✅ API端点配置正确")
        if 'checkStatus' in content:
            print("✅ 状态检查功能已实现")
        if 'testConnection' in content:
            print("✅ 连接测试功能已实现")
        if 'showConfig' in content:
            print("✅ 配置显示功能已实现")
            
        print("✅ 浏览器扩展集成测试通过")
        return True
    else:
        print("❌ 浏览器扩展示例文件不存在")
        return False

def main():
    """主测试函数"""
    print("🧪 开始API功能验证测试")
    print("=" * 50)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("VPN状态检查", test_vpn_status()))
    test_results.append(("VPN配置获取", test_vpn_config()))
    test_results.append(("连接测试", test_connection_test()))
    test_results.append(("系统信息", test_system_info()))
    test_results.append(("浏览器扩展集成", test_browser_extension_integration()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 API功能验证结果:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有API功能验证通过！")
        return True
    else:
        print("⚠️ 部分API功能需要修复")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
