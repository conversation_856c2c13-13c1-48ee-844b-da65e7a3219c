# Mac VPN连接故障排除指南

## 当前服务器状态

✅ **服务器连接**: ************* - 正常  
✅ **SSH连接**: 正常工作  
✅ **VPN端口**: UDP 500, 4500 - 正常监听  
✅ **IPSec服务**: 正常运行  

## 连接失败的可能原因

### 1. 服务器配置问题

**当前配置分析**:
- 使用IKEv2协议 ✅
- 支持PSK认证 ✅
- 配置了服务器证书 ✅
- 但缺少客户端IP池配置 ❌

**解决方案**: 添加客户端IP池配置

### 2. Mac客户端配置问题

**常见问题**:
- 认证方式选择错误
- 服务器ID配置错误
- 证书信任问题

## 修复步骤

### 步骤1: 修复服务器配置

在服务器上执行以下命令：

```bash
# SSH连接到服务器
ssh -i Google-VPN-Key.pem ec2-user@*************

# 修复IPSec配置
sudo tee /etc/ipsec.conf << 'EOF'
config setup
    protostack=netkey
    dnssec-enable=no

conn %default
    ikev2=insist
    left=%defaultroute
    right=%any
    auto=add

# PSK连接 (Mac推荐)
conn Google-VPN
    also=%default
    authby=secret
    rightsourceip=***********-***********0
    rightdns=*******,*******
EOF

# 重启IPSec服务
sudo systemctl restart ipsec
```

### 步骤2: Mac客户端配置

#### 方法1: PSK认证 (推荐)

1. **打开网络设置**
   - 系统偏好设置 → 网络
   - 点击 "+" → VPN → IKEv2

2. **基本配置**
   - 服务名称: `Google VPN`
   - 服务器地址: `*************`
   - 远程ID: `*************`
   - 本地ID: (留空)

3. **认证设置**
   - 认证方法: `共享密钥`
   - 共享密钥: `GoogleVPN2023!`

#### 方法2: 用户名密码认证

1. **基本配置** (同上)

2. **认证设置**
   - 认证方法: `用户名`
   - 用户名: `vpnuser`
   - 密码: `GoogleVPN2023!`

### 步骤3: 高级配置

如果基本配置失败，尝试以下高级设置：

1. **点击 "高级..."**
2. **选项标签页**:
   - ✅ 通过VPN连接发送所有流量
   - ✅ 断开连接时注销

3. **TCP/IP标签页**:
   - 配置IPv4: 使用PPP
   - 配置IPv6: 自动

4. **DNS标签页**:
   - DNS服务器: `*******, *******`

## 连接测试

### 测试步骤

1. **连接VPN**
   - 点击 "连接"
   - 观察状态变化

2. **验证连接**
   ```bash
   # 检查IP地址
   curl https://api.ipify.org
   # 应该显示: *************
   
   # 测试DNS解析
   nslookup google.com
   
   # 测试网站访问
   curl -I https://www.google.com
   ```

3. **检查路由**
   ```bash
   netstat -rn | grep default
   ```

## 故障排除

### 连接失败

**症状**: 点击连接后显示"连接失败"

**可能原因**:
1. 服务器配置错误
2. 认证信息错误
3. 网络阻止VPN流量

**解决方案**:
1. 检查服务器IPSec日志:
   ```bash
   ssh -i Google-VPN-Key.pem ec2-user@*************
   sudo journalctl -u ipsec -f
   ```

2. 检查Mac系统日志:
   ```bash
   log show --predicate 'subsystem == "com.apple.networkextension"' --last 5m
   ```

### 连接成功但无法上网

**症状**: VPN显示已连接，但无法访问网站

**可能原因**:
1. 路由配置错误
2. DNS配置错误
3. 服务器端NAT未配置

**解决方案**:
1. 检查服务器NAT配置:
   ```bash
   sudo iptables -t nat -L -n
   ```

2. 添加NAT规则:
   ```bash
   sudo iptables -t nat -A POSTROUTING -o enX0 -j MASQUERADE
   ```

### IP地址未改变

**症状**: VPN连接成功，但IP地址仍是本地IP

**解决方案**:
1. 确保选择了"通过VPN连接发送所有流量"
2. 检查路由表
3. 重启网络服务

## 备用连接方式

如果IKEv2连接失败，可以尝试：

### L2TP/IPSec (备用)

1. **VPN类型**: 选择 `L2TP over IPSec`
2. **服务器地址**: `*************`
3. **账户名称**: `vpnuser`
4. **密码**: `GoogleVPN2023!`
5. **共享密钥**: `GoogleVPN2023!`

### 第三方客户端

推荐使用：
- **Tunnelblick** (OpenVPN)
- **IKEv2 VPN** (App Store)
- **StrongSwan** (命令行)

## 技术支持

### 服务器端检查命令

```bash
# 检查IPSec状态
sudo ipsec status

# 检查连接日志
sudo journalctl -u ipsec --since "5 minutes ago"

# 检查端口监听
sudo ss -ulnp | grep -E ":(500|4500)"

# 检查防火墙
sudo iptables -L -n
```

### Mac端检查命令

```bash
# 检查VPN连接
scutil --nc list

# 检查路由
netstat -rn

# 检查DNS
scutil --dns

# 测试连通性
ping -c 3 *******
```

## 常见错误代码

- **错误 -1**: 服务器不可达
- **错误 -2**: 认证失败
- **错误 -3**: 协议不匹配
- **错误 -4**: 网络配置错误

## 联系支持

如果以上方法都无法解决问题，请提供：
1. Mac系统版本
2. 错误截图
3. 系统日志
4. 网络环境信息

---

**最后更新**: 2025-08-02  
**服务器**: *************  
**支持协议**: IKEv2, L2TP/IPSec
