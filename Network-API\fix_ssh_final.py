import boto3
import json
from botocore.exceptions import ClientError
import logging
import sys
import os

# --- Configuration ---
LOGGING_LEVEL = logging.INFO
DEPLOYMENT_INFO_FILE = 'vpn_deployment_info.json'

# --- Setup Logging ---
logging.basicConfig(level=LOGGING_LEVEL, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_ssh_fix_script():
    """Generates a script to completely reset and rebuild the SSH configuration."""
    
    script = """
    #!/bin/bash
    exec > /var/log/ssh_fix_script.log 2>&1
    echo "--- Starting SSH Configuration Reset ---"
    date

    # 1. Backup existing configuration
    echo "Backing up /etc/ssh/sshd_config to /etc/ssh/sshd_config.bak..."
    cp /etc/ssh/sshd_config /etc/ssh/sshd_config.bak || echo "Backup failed, continuing..."

    # 2. Create a new, known-good sshd_config
    echo "Generating a new, minimal sshd_config..."
    cat > /etc/ssh/sshd_config << 'EOF'
# Default sshd_config
Port 22
Protocol 2
HostKey /etc/ssh/ssh_host_rsa_key
HostKey /etc/ssh/ssh_host_dsa_key
HostKey /etc/ssh/ssh_host_ecdsa_key
HostKey /etc/ssh/ssh_host_ed25519_key
UsePrivilegeSeparation yes
KeyRegenerationInterval 3600
ServerKeyBits 1024
SyslogFacility AUTH
LogLevel INFO
LoginGraceTime 120
PermitRootLogin no
StrictModes yes
RSAAuthentication yes
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
IgnoreRhosts yes
RhostsRSAAuthentication no
HostbasedAuthentication no
PermitEmptyPasswords no
ChallengeResponseAuthentication no
PasswordAuthentication no
X11Forwarding yes
X11DisplayOffset 10
PrintMotd no
PrintLastLog yes
TCPKeepAlive yes
AcceptEnv LANG LC_*
Subsystem sftp /usr/libexec/openssh/sftp-server
UsePAM yes
EOF

    # 3. Ensure TCP Wrappers are not blocking SSH
    echo "Checking TCP Wrappers (hosts.allow, hosts.deny)..."
    if [ -f /etc/hosts.deny ] && grep -q "sshd: ALL" /etc/hosts.deny; then
        echo "Found blocking rule in /etc/hosts.deny, commenting it out..."
        sed -i 's/sshd: ALL/#sshd: ALL/' /etc/hosts.deny
    fi
    if [ ! -f /etc/hosts.allow ] || ! grep -q "sshd: ALL" /etc/hosts.allow; then
        echo "Ensuring sshd is allowed in /etc/hosts.allow..."
        echo "sshd: ALL" >> /etc/hosts.allow
    fi

    # 4. Regenerate server's host keys
    echo "Regenerating SSH host keys..."
    ssh-keygen -A

    # 5. Verify permissions for ec2-user's authorized_keys
    echo "Verifying permissions for /home/<USER>/.ssh/authorized_keys..."
    chown -R ec2-user:ec2-user /home/<USER>/.ssh
    chmod 700 /home/<USER>/.ssh
    chmod 600 /home/<USER>/.ssh/authorized_keys

    # 6. Restart the SSH service
    echo "Restarting sshd service..."
    systemctl restart sshd

    # 7. Final status check
    echo "Final status of sshd service:"
    systemctl status sshd

    echo "--- SSH Configuration Reset Completed ---"
    """
    return script

def main():
    """Main function."""
    logger.info("--- SSH Final Fix Tool ---")
    
    # Load deployment info
    if not os.path.exists(DEPLOYMENT_INFO_FILE):
        logger.error(f"Deployment info file '{DEPLOYMENT_INFO_FILE}' not found.")
        sys.exit(1)
    with open(DEPLOYMENT_INFO_FILE, 'r') as f:
        deployment_info = json.load(f)
    
    instance_id = deployment_info.get('instance_id')
    region = deployment_info.get('region')
    
    if not instance_id or not region:
        logger.error("Instance ID or region not found in deployment info file.")
        sys.exit(1)
        
    ssm_client = boto3.client('ssm', region_name=region)
    
    fix_script = get_ssh_fix_script()
    
    logger.info("Sending SSH fix script to instance via SSM...")
    try:
        response = ssm_client.send_command(
            InstanceIds=[instance_id],
            DocumentName='AWS-RunShellScript',
            Parameters={'commands': [fix_script]},
            TimeoutSeconds=300,
            Comment='Attempt to fix SSH by resetting configuration'
        )
        command_id = response['Command']['CommandId']
        
        waiter = ssm_client.get_waiter('command_executed')
        waiter.wait(CommandId=command_id, InstanceId=instance_id, WaiterConfig={'Delay': 10, 'MaxAttempts': 30})
        
        output = ssm_client.get_command_invocation(CommandId=command_id, InstanceId=instance_id)
        
        print("\n--- SCRIPT EXECUTION RESULT ---")
        if output['Status'] == 'Success':
            logger.info("✅ SSH fix script executed successfully.")
            print(output.get('StandardOutputContent', ''))
        else:
            logger.error(f"❌ SSH fix script failed with status: {output['Status']}")
            print("--- STDOUT ---")
            print(output.get('StandardOutputContent', ''))
            print("--- STDERR ---")
            print(output.get('StandardErrorContent', ''))
        print("-----------------------------")

    except Exception as e:
        logger.error(f"An error occurred: {e}")

if __name__ == "__main__":
    main()
