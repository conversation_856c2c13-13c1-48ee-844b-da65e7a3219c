#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AWS资源检查脚本
检查VPN项目相关的AWS资源，识别重复部署
"""

import boto3
import json
import sys
from botocore.exceptions import ClientError
from datetime import datetime

class AWSResourceChecker:
    def __init__(self, region='us-east-1'):
        self.region = region
        try:
            self.ec2 = boto3.client('ec2', region_name=region)
            self.ec2_resource = boto3.resource('ec2', region_name=region)
        except Exception as e:
            print(f"错误：无法初始化AWS客户端: {e}")
            sys.exit(1)
    
    def check_vpn_instances(self):
        """检查VPN相关的EC2实例"""
        print("=== 检查VPN EC2实例 ===")
        try:
            # 查找所有标记为Google-VPN-Server的实例
            response = self.ec2.describe_instances(
                Filters=[
                    {'Name': 'tag:Name', 'Values': ['Google-VPN-Server']},
                    {'Name': 'instance-state-name', 'Values': ['running', 'stopped', 'stopping', 'starting', 'pending']}
                ]
            )
            
            instances = []
            for reservation in response['Reservations']:
                for instance in reservation['Instances']:
                    instances.append({
                        'InstanceId': instance['InstanceId'],
                        'State': instance['State']['Name'],
                        'PublicIpAddress': instance.get('PublicIpAddress', 'N/A'),
                        'PrivateIpAddress': instance.get('PrivateIpAddress', 'N/A'),
                        'LaunchTime': instance['LaunchTime'].strftime('%Y-%m-%d %H:%M:%S'),
                        'InstanceType': instance['InstanceType'],
                        'VpcId': instance.get('VpcId', 'N/A'),
                        'SubnetId': instance.get('SubnetId', 'N/A')
                    })
            
            if not instances:
                print("未找到标记为'Google-VPN-Server'的实例")
                return []
            
            print(f"找到 {len(instances)} 个VPN实例:")
            for i, instance in enumerate(instances, 1):
                print(f"\n实例 {i}:")
                print(f"  实例ID: {instance['InstanceId']}")
                print(f"  状态: {instance['State']}")
                print(f"  公网IP: {instance['PublicIpAddress']}")
                print(f"  私网IP: {instance['PrivateIpAddress']}")
                print(f"  启动时间: {instance['LaunchTime']}")
                print(f"  实例类型: {instance['InstanceType']}")
                print(f"  VPC ID: {instance['VpcId']}")
                print(f"  子网ID: {instance['SubnetId']}")
            
            if len(instances) > 1:
                print(f"\n⚠️  警告：发现 {len(instances)} 个VPN实例，可能存在重复部署！")
            
            return instances
            
        except ClientError as e:
            print(f"检查实例时出错: {e}")
            return []
    
    def check_vpn_vpcs(self):
        """检查VPN相关的VPC"""
        print("\n=== 检查VPN VPC ===")
        try:
            response = self.ec2.describe_vpcs(
                Filters=[
                    {'Name': 'tag:Name', 'Values': ['Google-VPN-VPC']}
                ]
            )
            
            vpcs = []
            for vpc in response['Vpcs']:
                vpcs.append({
                    'VpcId': vpc['VpcId'],
                    'State': vpc['State'],
                    'CidrBlock': vpc['CidrBlock'],
                    'IsDefault': vpc['IsDefault']
                })
            
            if not vpcs:
                print("未找到标记为'Google-VPN-VPC'的VPC")
                return []
            
            print(f"找到 {len(vpcs)} 个VPN VPC:")
            for i, vpc in enumerate(vpcs, 1):
                print(f"\nVPC {i}:")
                print(f"  VPC ID: {vpc['VpcId']}")
                print(f"  状态: {vpc['State']}")
                print(f"  CIDR块: {vpc['CidrBlock']}")
                print(f"  是否默认: {vpc['IsDefault']}")
            
            if len(vpcs) > 1:
                print(f"\n⚠️  警告：发现 {len(vpcs)} 个VPN VPC，可能存在重复部署！")
            
            return vpcs
            
        except ClientError as e:
            print(f"检查VPC时出错: {e}")
            return []
    
    def check_security_groups(self):
        """检查VPN相关的安全组"""
        print("\n=== 检查VPN安全组 ===")
        try:
            response = self.ec2.describe_security_groups(
                Filters=[
                    {'Name': 'group-name', 'Values': ['Google-VPN-SG']}
                ]
            )
            
            security_groups = []
            for sg in response['SecurityGroups']:
                security_groups.append({
                    'GroupId': sg['GroupId'],
                    'GroupName': sg['GroupName'],
                    'Description': sg['Description'],
                    'VpcId': sg['VpcId']
                })
            
            if not security_groups:
                print("未找到名为'Google-VPN-SG'的安全组")
                return []
            
            print(f"找到 {len(security_groups)} 个VPN安全组:")
            for i, sg in enumerate(security_groups, 1):
                print(f"\n安全组 {i}:")
                print(f"  安全组ID: {sg['GroupId']}")
                print(f"  名称: {sg['GroupName']}")
                print(f"  描述: {sg['Description']}")
                print(f"  VPC ID: {sg['VpcId']}")
            
            if len(security_groups) > 1:
                print(f"\n⚠️  警告：发现 {len(security_groups)} 个VPN安全组，可能存在重复部署！")
            
            return security_groups
            
        except ClientError as e:
            print(f"检查安全组时出错: {e}")
            return []
    
    def check_elastic_ips(self):
        """检查弹性IP"""
        print("\n=== 检查弹性IP ===")
        try:
            response = self.ec2.describe_addresses()
            
            vpn_eips = []
            for address in response['Addresses']:
                # 检查是否关联到VPN实例
                if 'InstanceId' in address:
                    instance_response = self.ec2.describe_instances(InstanceIds=[address['InstanceId']])
                    for reservation in instance_response['Reservations']:
                        for instance in reservation['Instances']:
                            for tag in instance.get('Tags', []):
                                if tag['Key'] == 'Name' and tag['Value'] == 'Google-VPN-Server':
                                    vpn_eips.append({
                                        'PublicIp': address['PublicIp'],
                                        'AllocationId': address['AllocationId'],
                                        'InstanceId': address.get('InstanceId', 'N/A'),
                                        'AssociationId': address.get('AssociationId', 'N/A')
                                    })
                                    break
            
            if not vpn_eips:
                print("未找到与VPN实例关联的弹性IP")
                return []
            
            print(f"找到 {len(vpn_eips)} 个VPN弹性IP:")
            for i, eip in enumerate(vpn_eips, 1):
                print(f"\n弹性IP {i}:")
                print(f"  公网IP: {eip['PublicIp']}")
                print(f"  分配ID: {eip['AllocationId']}")
                print(f"  实例ID: {eip['InstanceId']}")
                print(f"  关联ID: {eip['AssociationId']}")
            
            return vpn_eips
            
        except ClientError as e:
            print(f"检查弹性IP时出错: {e}")
            return []
    
    def check_key_pairs(self):
        """检查密钥对"""
        print("\n=== 检查密钥对 ===")
        try:
            response = self.ec2.describe_key_pairs(
                Filters=[
                    {'Name': 'key-name', 'Values': ['Google-VPN-Key']}
                ]
            )
            
            key_pairs = []
            for kp in response['KeyPairs']:
                key_pairs.append({
                    'KeyName': kp['KeyName'],
                    'KeyPairId': kp['KeyPairId'],
                    'KeyFingerprint': kp['KeyFingerprint']
                })
            
            if not key_pairs:
                print("未找到名为'Google-VPN-Key'的密钥对")
                return []
            
            print(f"找到 {len(key_pairs)} 个VPN密钥对:")
            for i, kp in enumerate(key_pairs, 1):
                print(f"\n密钥对 {i}:")
                print(f"  名称: {kp['KeyName']}")
                print(f"  密钥对ID: {kp['KeyPairId']}")
                print(f"  指纹: {kp['KeyFingerprint']}")
            
            return key_pairs

        except ClientError as e:
            print(f"检查密钥对时出错: {e}")
            return []

    def generate_cleanup_recommendations(self, instances, vpcs, security_groups, eips):
        """生成清理建议"""
        print("\n" + "="*50)
        print("清理建议")
        print("="*50)

        if len(instances) > 1:
            print(f"\n🔧 发现 {len(instances)} 个VPN实例，建议保留最新的一个：")
            # 按启动时间排序，保留最新的
            sorted_instances = sorted(instances, key=lambda x: x['LaunchTime'], reverse=True)
            print(f"   保留: {sorted_instances[0]['InstanceId']} (启动时间: {sorted_instances[0]['LaunchTime']})")
            print("   删除以下实例:")
            for instance in sorted_instances[1:]:
                print(f"     - {instance['InstanceId']} (启动时间: {instance['LaunchTime']})")

        if len(vpcs) > 1:
            print(f"\n🔧 发现 {len(vpcs)} 个VPN VPC，建议只保留一个")

        if len(security_groups) > 1:
            print(f"\n🔧 发现 {len(security_groups)} 个VPN安全组，建议只保留一个")

        if len(eips) > 1:
            print(f"\n🔧 发现 {len(eips)} 个VPN弹性IP，建议只保留一个")

        if len(instances) <= 1 and len(vpcs) <= 1 and len(security_groups) <= 1:
            print("\n✅ 未发现重复资源，当前部署状态正常")

def main():
    print("AWS VPN资源检查工具")
    print("==================")

    # 检查AWS凭证
    try:
        sts = boto3.client('sts')
        identity = sts.get_caller_identity()
        print(f"AWS账户ID: {identity['Account']}")
        print(f"用户ARN: {identity['Arn']}")
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    except Exception as e:
        print(f"错误：无法验证AWS凭证: {e}")
        return

    checker = AWSResourceChecker()

    # 执行各项检查
    instances = checker.check_vpn_instances()
    vpcs = checker.check_vpn_vpcs()
    security_groups = checker.check_security_groups()
    eips = checker.check_elastic_ips()
    key_pairs = checker.check_key_pairs()

    # 生成清理建议
    checker.generate_cleanup_recommendations(instances, vpcs, security_groups, eips)

if __name__ == "__main__":
    main()
