import boto3
import json
from botocore.exceptions import ClientError
import logging
import configparser
import os
import sys
import time

class GoogleVPNDeployment:
    def __init__(self, region='us-east-1'):
        self.region = region
        try:
            self.ec2 = boto3.client('ec2', region_name=region)
            self.iam = boto3.client('iam', region_name=region)
            self.cloudformation = boto3.client('cloudformation', region_name=region)
        except Exception as e:
            print(f"错误：无法初始化AWS客户端: {e}")
            sys.exit(1)
        
        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.config = configparser.ConfigParser()
        config_file = 'vpn_config.ini'
        if os.path.exists(config_file):
            self.config.read(config_file)
            self.logger.info(f"配置文件 {config_file} 已加载")
        else:
            self.logger.warning(f"配置文件 {config_file} 不存在，将使用默认配置")
            self.config['VPN'] = {'PreSharedKey': 'GoogleVPN2023!'}
        
    def get_existing_vpc_resources(self):
        """检查是否存在已有的VPC资源可以复用"""
        try:
            # 先检查vpn_deployment_info.json文件中是否已有部署信息
            if os.path.exists('vpn_deployment_info.json'):
                with open('vpn_deployment_info.json', 'r') as f:
                    deployment_info = json.load(f)
                
                instance_id = deployment_info.get('instance_id')
                vpc_id = deployment_info.get('vpc_id')
                subnet_id = deployment_info.get('subnet_id')
                security_group_id = deployment_info.get('security_group_id')
                
                # 验证这些资源是否仍然存在
                try:
                    # 检查实例是否存在
                    instances = self.ec2.describe_instances(InstanceIds=[instance_id])
                    if not instances['Reservations']:
                        self.logger.info("记录的实例不存在，需要重新创建资源")
                        return None
                    
                    # 检查VPC是否存在
                    vpcs = self.ec2.describe_vpcs(VpcIds=[vpc_id])
                    if not vpcs['Vpcs']:
                        self.logger.info("记录的VPC不存在，需要重新创建资源")
                        return None
                    
                    # 检查子网是否存在
                    subnets = self.ec2.describe_subnets(SubnetIds=[subnet_id])
                    if not subnets['Subnets']:
                        self.logger.info("记录的子网不存在，需要重新创建资源")
                        return None
                    
                    # 检查安全组是否存在
                    security_groups = self.ec2.describe_security_groups(GroupIds=[security_group_id])
                    if not security_groups['SecurityGroups']:
                        self.logger.info("记录的安全组不存在，需要重新创建资源")
                        return None
                    
                    self.logger.info(f"找到并验证现有资源: VPC={vpc_id}, 子网={subnet_id}, 安全组={security_group_id}")
                    return {
                        'vpc_id': vpc_id,
                        'subnet_id': subnet_id,
                        'security_group_id': security_group_id
                    }
                except ClientError:
                    self.logger.info("验证现有资源时出错，可能需要重新创建")
                    return None
            
            # 如果没有部署信息文件，则查找标记为Google-VPN-VPC的VPC
            vpcs = self.ec2.describe_vpcs(Filters=[
                {'Name': 'tag:Name', 'Values': ['Google-VPN-VPC']}
            ])
            
            if vpcs['Vpcs']:
                vpc_id = vpcs['Vpcs'][0]['VpcId']
                self.logger.info(f"找到现有的VPC: {vpc_id}")
                
                # 查找关联的子网
                subnets = self.ec2.describe_subnets(Filters=[
                    {'Name': 'vpc-id', 'Values': [vpc_id]}
                ])
                
                if subnets['Subnets']:
                    subnet_id = subnets['Subnets'][0]['SubnetId']
                    self.logger.info(f"找到现有的子网: {subnet_id}")
                    
                    # 查找安全组
                    security_groups = self.ec2.describe_security_groups(Filters=[
                        {'Name': 'vpc-id', 'Values': [vpc_id]},
                        {'Name': 'group-name', 'Values': ['Google-VPN-SG']}
                    ])
                    
                    if security_groups['SecurityGroups']:
                        sg_id = security_groups['SecurityGroups'][0]['GroupId']
                        self.logger.info(f"找到现有的安全组: {sg_id}")
                        
                        return {
                            'vpc_id': vpc_id,
                            'subnet_id': subnet_id,
                            'security_group_id': sg_id
                        }
            return None
        except ClientError as e:
            self.logger.warning(f"检查现有VPC资源时出错: {e}")
            return None

    def create_vpc(self):
        """创建VPC用于VPN部署"""
        try:
            self.logger.info("正在创建VPC...")
            # 创建VPC
            vpc_response = self.ec2.create_vpc(
                CidrBlock='10.0.0.0/16',
                TagSpecifications=[
                    {
                        'ResourceType': 'vpc',
                        'Tags': [
                            {
                                'Key': 'Name',
                                'Value': 'Google-VPN-VPC'
                            }
                        ]
                    }
                ]
            )
            vpc_id = vpc_response['Vpc']['VpcId']
            self.logger.info(f"VPC创建成功: {vpc_id}")
            
            # 启用DNS支持
            self.ec2.modify_vpc_attribute(
                VpcId=vpc_id,
                EnableDnsSupport={'Value': True}
            )
            self.ec2.modify_vpc_attribute(
                VpcId=vpc_id,
                EnableDnsHostnames={'Value': True}
            )
            
            # 创建子网
            self.logger.info("正在创建子网...")
            subnet_response = self.ec2.create_subnet(
                VpcId=vpc_id,
                CidrBlock='********/24',
                AvailabilityZone=self.region + 'a'
            )
            subnet_id = subnet_response['Subnet']['SubnetId']
            self.logger.info(f"子网创建成功: {subnet_id}")
            
            # 创建互联网网关
            self.logger.info("正在创建互联网网关...")
            igw_response = self.ec2.create_internet_gateway()
            igw_id = igw_response['InternetGateway']['InternetGatewayId']
            self.logger.info(f"互联网网关创建成功: {igw_id}")
            
            # 将互联网网关附加到VPC
            self.ec2.attach_internet_gateway(
                InternetGatewayId=igw_id,
                VpcId=vpc_id
            )
            self.logger.info("互联网网关已附加到VPC")
            
            # 创建路由表
            self.logger.info("正在创建路由表...")
            route_table_response = self.ec2.create_route_table(VpcId=vpc_id)
            route_table_id = route_table_response['RouteTable']['RouteTableId']
            self.logger.info(f"路由表创建成功: {route_table_id}")
            
            # 添加默认路由到互联网网关
            self.ec2.create_route(
                RouteTableId=route_table_id,
                DestinationCidrBlock='0.0.0.0/0',
                GatewayId=igw_id
            )
            self.logger.info("默认路由已添加到互联网网关")
            
            # 关联子网和路由表
            self.ec2.associate_route_table(
                SubnetId=subnet_id,
                RouteTableId=route_table_id
            )
            self.logger.info("子网和路由表已关联")
            
            return {
                'vpc_id': vpc_id,
                'subnet_id': subnet_id,
                'internet_gateway_id': igw_id,
                'route_table_id': route_table_id
            }
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'UnauthorizedOperation':
                print("权限错误：当前AWS用户没有足够的权限执行此操作。")
                print("请确保您的用户具有适当的EC2权限，例如AmazonEC2FullAccess策略。")
            elif error_code == 'VpcLimitExceeded':
                print("错误：VPC数量已达上限。")
                print("请删除不需要的VPC或申请提高VPC配额。")
                print("或者使用已有的VPC资源。")
            print(f"创建VPC时出错: {e}")
            return None
        except Exception as e:
            print(f"创建VPC时发生未知错误: {e}")
            return None
    
    def create_security_group(self, vpc_id):
        """创建安全组"""
        try:
            self.logger.info("正在创建安全组...")
            sg_response = self.ec2.create_security_group(
                GroupName='Google-VPN-SG',
                Description='Security group for Google VPN access',
                VpcId=vpc_id
            )
            sg_id = sg_response['GroupId']
            self.logger.info(f"安全组创建成功: {sg_id}")
            
            # 添加入站规则
            self.logger.info("正在配置安全组入站规则...")
            self.ec2.authorize_security_group_ingress(
                GroupId=sg_id,
                IpPermissions=[
                    {
                        'IpProtocol': 'tcp',
                        'FromPort': 22,
                        'ToPort': 22,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    },
                    {
                        'IpProtocol': 'udp',
                        'FromPort': 500,
                        'ToPort': 500,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    },
                    {
                        'IpProtocol': 'udp',
                        'FromPort': 4500,
                        'ToPort': 4500,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    },
                    {
                        'IpProtocol': '50',  # ESP协议
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    }
                ]
            )
            self.logger.info("安全组入站规则配置完成")
            
            # 注意：安全组默认允许所有出站流量，无需额外配置
            
            return sg_id
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'UnauthorizedOperation':
                print("权限错误：当前AWS用户没有足够的权限执行此操作。")
                print("请确保您的用户具有适当的EC2权限，例如AmazonEC2FullAccess策略。")
            print(f"创建安全组时出错: {e}")
            return None
        except Exception as e:
            print(f"创建安全组时发生未知错误: {e}")
            return None
    
    def create_vpn_instance(self, subnet_id, security_group_id):
        """创建VPN实例"""
        try:
            self.logger.info("正在创建VPN实例...")
            # 使用Amazon Linux 2 AMI (需要根据实际区域调整AMI ID)
            ami_id = self.get_ami_id()
            
            # 检查是否已有标记为Google-VPN-Server的实例
            existing_instances = self.ec2.describe_instances(Filters=[
                {'Name': 'tag:Name', 'Values': ['Google-VPN-Server']},
                {'Name': 'instance-state-name', 'Values': ['running', 'stopped', 'stopping', 'starting']}
            ])
            
            # 如果有现有的VPN实例，直接返回其信息
            for reservation in existing_instances.get('Reservations', []):
                for instance in reservation.get('Instances', []):
                    instance_id = instance['InstanceId']
                    public_ip = None
                    
                    # 获取实例的公共IP（优先弹性IP，然后是实例公有IP）
                    if 'PublicIpAddress' in instance:
                        public_ip = instance['PublicIpAddress']
                    else:
                        # 如果没有公共IP，尝试查找关联的弹性IP
                        try:
                            addresses = self.ec2.describe_addresses()
                            for address in addresses.get('Addresses', []):
                                if address.get('InstanceId') == instance_id:
                                    public_ip = address['PublicIp']
                                    break
                        except ClientError:
                            self.logger.warning("无法查询弹性IP地址")
                    
                    self.logger.info(f"使用现有的VPN实例: {instance_id}")
                    self.logger.info(f"实例公共IP: {public_ip}")
                    return {
                        'instance_id': instance_id,
                        'public_ip': public_ip,
                        'allocation_id': None  # 现有实例可能没有allocation_id
                    }
            
            # 没有找到现有实例，创建新实例
            instance_response = self.ec2.run_instances(
                ImageId=ami_id,
                MinCount=1,
                MaxCount=1,
                InstanceType='t3.micro',
                KeyName=self.create_key_pair(),
                SecurityGroupIds=[security_group_id],
                SubnetId=subnet_id,
                UserData=self.get_vpn_setup_script(),
                TagSpecifications=[
                    {
                        'ResourceType': 'instance',
                        'Tags': [
                            {
                                'Key': 'Name',
                                'Value': 'Google-VPN-Server'
                            }
                        ]
                    }
                ]
            )
            
            instance_id = instance_response['Instances'][0]['InstanceId']
            self.logger.info(f"实例创建成功: {instance_id}")
            
            # 等待实例运行
            self.logger.info("等待实例启动...")
            waiter = self.ec2.get_waiter('instance_running')
            waiter.wait(InstanceIds=[instance_id])
            self.logger.info("实例已启动")
            
            # 分配弹性IP
            self.logger.info("正在分配弹性IP...")
            eip_response = self.ec2.allocate_address(Domain='vpc')
            public_ip = eip_response['PublicIp']
            allocation_id = eip_response['AllocationId']
            self.logger.info(f"弹性IP分配成功: {public_ip}")
            
            # 将弹性IP关联到实例
            self.ec2.associate_address(
                InstanceId=instance_id,
                AllocationId=allocation_id
            )
            self.logger.info("弹性IP已关联到实例")
            
            return {
                'instance_id': instance_id,
                'public_ip': public_ip,
                'allocation_id': allocation_id
            }
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'UnauthorizedOperation':
                print("权限错误：当前AWS用户没有足够的权限执行此操作。")
                print("请确保您的用户具有适当的EC2权限，例如AmazonEC2FullAccess策略。")
            elif error_code == 'InsufficientInstanceCapacity':
                print("错误：请求的实例类型在该区域暂时不可用。")
            elif error_code == 'InstanceLimitExceeded':
                print("错误：已达到实例限制。")
            print(f"创建VPN实例时出错: {e}")
            return None
        except Exception as e:
            print(f"创建VPN实例时发生未知错误: {e}")
            return None
    
    def get_ami_id(self):
        """获取AMI ID"""
        # 这里使用Amazon Linux 2的示例AMI ID，实际使用时需要根据区域调整
        # 可以通过describe_images API动态获取最新的AMI
        ami_mapping = {
            'us-east-1': 'ami-09d95fab7fff3776c',
            'us-west-2': 'ami-008fe2fc65df48dac',
            'eu-west-1': 'ami-0dad359ff462124ca'
        }
        return ami_mapping.get(self.region, 'ami-09d95fab7fff3776c')
    
    def create_key_pair(self):
        """创建密钥对"""
        try:
            key_name = 'Google-VPN-Key'
            # 检查密钥对是否已存在
            try:
                self.ec2.describe_key_pairs(KeyNames=[key_name])
                self.logger.info(f"密钥对 {key_name} 已存在")
                return key_name
            except ClientError:
                pass
            
            # 创建新密钥对
            self.logger.info(f"创建新的密钥对: {key_name}")
            key_response = self.ec2.create_key_pair(KeyName=key_name)
            # 实际应用中应该保存私钥内容到安全位置
            with open(f'{key_name}.pem', 'w') as f:
                f.write(key_response['KeyMaterial'])
            
            return key_name
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'UnauthorizedOperation':
                print("权限错误：当前AWS用户没有足够的权限执行此操作。")
                print("请确保您的用户具有适当的EC2权限，例如AmazonEC2FullAccess策略。")
            print(f"创建密钥对时出错: {e}")
            return None
        except Exception as e:
            print(f"创建密钥对时发生未知错误: {e}")
            return None
    
    def get_vpn_setup_script(self):
        """获取增强的多协议VPN服务器设置脚本"""
        # 从配置文件中读取预共享密钥
        try:
            psk = self.config.get('VPN', 'PreSharedKey')
        except (configparser.NoSectionError, configparser.NoOptionError):
            self.logger.warning("未找到配置文件或预共享密钥，使用默认值")
            psk = "GoogleVPN2023!"

        script = f'''#!/bin/bash
echo "开始配置增强多协议VPN服务器..."

# 更新系统并安装软件包
yum update -y
amazon-linux-extras install -y epel
yum install -y strongswan xl2tpd ppp

# 配置系统参数
echo 'net.ipv4.ip_forward = 1' >> /etc/sysctl.conf
echo 'net.ipv6.conf.all.forwarding = 1' >> /etc/sysctl.conf
echo 'net.ipv4.conf.all.accept_redirects = 0' >> /etc/sysctl.conf
echo 'net.ipv4.conf.all.send_redirects = 0' >> /etc/sysctl.conf
sysctl -p

# 配置增强的StrongSwan (支持多种客户端)
cat > /etc/strongswan/ipsec.conf << 'EOF'
config setup
    charondebug="ike 1, knl 1, cfg 0"
    uniqueids=no

conn %default
    left=%defaultroute
    leftsubnet=0.0.0.0/0
    right=%any
    rightsourceip=**********/24
    rightdns=*******,*******
    auto=add
    keyexchange=ikev2
    # 支持多种加密算法，兼容Windows/iOS/Android
    ike=aes256-sha256-modp2048,aes256-sha1-modp2048,aes128-sha256-modp2048,aes128-sha1-modp2048,aes256-sha1-modp1024,aes128-sha1-modp1024,3des-sha1-modp1024
    esp=aes256-sha256,aes256-sha1,aes128-sha256,aes128-sha1,3des-sha1
    dpdaction=clear
    dpddelay=300s
    rekey=no
    leftfirewall=yes

# PSK认证连接
conn Google-VPN-PSK
    also=%default
    leftauth=psk
    rightauth=psk
    rightsubnet=0.0.0.0/0

# 用户名密码认证连接
conn Google-VPN-EAP
    also=%default
    leftauth=psk
    rightauth=eap-mschapv2
    rightsubnet=0.0.0.0/0
    eap_identity=%identity
EOF

# 配置认证密钥
cat > /etc/strongswan/ipsec.secrets << 'EOF'
# PSK认证
: PSK "{psk}"

# 用户名密码认证
vpnuser : EAP "{psk}"
testuser : EAP "{psk}"
admin : EAP "{psk}"
EOF

# 配置L2TP/IPSec (备用协议)
cat > /etc/xl2tpd/xl2tpd.conf << 'EOF'
[global]
listen-addr = 0.0.0.0
port = 1701

[lns default]
ip range = ***********-************
local ip = **********
require chap = yes
refuse pap = yes
require authentication = yes
name = l2tpd
ppp debug = yes
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
EOF

cat > /etc/ppp/options.xl2tpd << 'EOF'
ipcp-accept-local
ipcp-accept-remote
ms-dns *******
ms-dns *******
noccp
auth
crtscts
idle 1800
mtu 1410
mru 1410
nodefaultroute
debug
lock
proxyarp
connect-delay 5000
EOF

cat > /etc/ppp/chap-secrets << 'EOF'
vpnuser l2tpd {psk} *
testuser l2tpd {psk} *
admin l2tpd {psk} *
EOF

# 配置防火墙规则
iptables -F
iptables -t nat -F

# IKEv2规则
iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -m policy --pol ipsec --dir out -j ACCEPT
iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE
iptables -A FORWARD -s **********/24 -j ACCEPT
iptables -A FORWARD -d **********/24 -j ACCEPT

# L2TP规则
iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE
iptables -A FORWARD -s **********/24 -j ACCEPT
iptables -A FORWARD -d **********/24 -j ACCEPT

# 允许VPN端口
iptables -A INPUT -p udp --dport 500 -j ACCEPT
iptables -A INPUT -p udp --dport 4500 -j ACCEPT
iptables -A INPUT -p udp --dport 1701 -j ACCEPT
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# 保存iptables规则
iptables-save > /etc/iptables.rules

# 启动服务
systemctl enable strongswan
systemctl start strongswan
systemctl enable xl2tpd
systemctl start xl2tpd

# 配置开机自启iptables
cat > /etc/systemd/system/iptables-restore.service << 'EOF'
[Unit]
Description=Restore iptables rules
After=network.target

[Service]
Type=oneshot
ExecStart=/sbin/iptables-restore /etc/iptables.rules

[Install]
WantedBy=multi-user.target
EOF

systemctl enable iptables-restore

echo "🎉 多协议VPN服务器配置完成！"
echo "支持协议: IKEv2, L2TP/IPSec"
echo "预共享密钥: {psk}"
echo "用户名: vpnuser/testuser/admin"
echo "密码: {psk}"
'''
        import base64
        return base64.b64encode(script.encode()).decode()
    
    def deploy_vpn(self):
        """部署完整的VPN解决方案"""
        self.logger.info("开始部署Google VPN服务...")
        
        # 首先检查是否有可用的现有VPC资源
        vpc_info = self.get_existing_vpc_resources()
        if vpc_info:
            self.logger.info("使用现有的VPC资源")
        else:
            # 创建VPC基础设施
            self.logger.info("1. 创建VPC基础设施...")
            vpc_info = self.create_vpc()
            if not vpc_info:
                self.logger.error("VPC创建失败")
                return False
        
        # 如果没有现有的安全组，则创建安全组
        if 'security_group_id' not in vpc_info:
            self.logger.info("2. 创建安全组...")
            sg_id = self.create_security_group(vpc_info['vpc_id'])
            if not sg_id:
                self.logger.error("安全组创建失败")
                return False
            vpc_info['security_group_id'] = sg_id
        else:
            self.logger.info("2. 使用现有的安全组...")
        
        # 创建VPN实例
        self.logger.info("3. 创建VPN实例...")
        instance_info = self.create_vpn_instance(vpc_info['subnet_id'], vpc_info['security_group_id'])
        if not instance_info:
            self.logger.error("VPN实例创建失败")
            return False
        
        self.logger.info(f"VPN部署完成!")
        self.logger.info(f"VPN服务器公网IP: {instance_info['public_ip']}")
        
        # 从配置文件或使用默认值获取预共享密钥
        psk = self.config.get('VPN', 'PreSharedKey', fallback='GoogleVPN2023!')
        self.logger.info(f"预共享密钥: {psk}")
        self.logger.info(f"客户端配置建议:")
        self.logger.info(f"  - 服务器地址: {instance_info['public_ip']}")
        self.logger.info(f"  - IPSec类型: IKEv2")
        self.logger.info(f"  - 认证方式: 预共享密钥")
        self.logger.info(f"  - 远程ID: {instance_info['public_ip']}")
        self.logger.info(f"  - 本地ID: (留空)")
        
        # 保存部署信息到文件
        deployment_info = {
            'instance_id': instance_info['instance_id'],
            'public_ip': instance_info['public_ip'],
            'vpc_id': vpc_info['vpc_id'],
            'subnet_id': vpc_info['subnet_id'],
            'security_group_id': vpc_info['security_group_id'],
            'region': self.region
        }
        
        with open('vpn_deployment_info.json', 'w') as f:
            json.dump(deployment_info, f, indent=2)
        
        self.logger.info("部署信息已保存到 vpn_deployment_info.json")
        
        return True

def main():
    print("Network-API VPN 部署工具")
    print("========================")
    
    # 检查AWS凭证
    try:
        sts = boto3.client('sts')
        identity = sts.get_caller_identity()
        print(f"AWS账户ID: {identity['Account']}")
        print(f"用户ARN: {identity['Arn']}")
    except Exception as e:
        print(f"错误：无法验证AWS凭证: {e}")
        print("请确保已正确配置AWS凭证。")
        return
    
    # 创建部署实例
    vpn_deploy = GoogleVPNDeployment()
    
    # 开始部署
    success = vpn_deploy.deploy_vpn()
    
    if success:
        print("\nVPN部署成功完成！")
        print("请查看日志和 vpn_deployment_info.json 文件获取详细信息。")
    else:
        print("\nVPN部署失败！")
        print("请查看错误信息并解决后重试。")

if __name__ == "__main__":
    main()