#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试IPSec服务问题
"""

import boto3
import json
import time

def send_command(ssm, instance_id, commands, description):
    """发送单个命令"""
    try:
        print(f"\n🔧 {description}")
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={'commands': commands},
            TimeoutSeconds=120
        )
        
        command_id = response['Command']['CommandId']
        
        # 等待命令完成
        for i in range(30):
            time.sleep(4)
            try:
                result = ssm.get_command_invocation(
                    CommandId=command_id,
                    InstanceId=instance_id
                )
                
                status = result['Status']
                if status == 'Success':
                    output = result['StandardOutputContent']
                    if output.strip():
                        print(f"✅ 输出:\n{output}")
                    else:
                        print("✅ 成功")
                    return True
                elif status == 'Failed':
                    error = result['StandardErrorContent']
                    print(f"❌ 失败: {error}")
                    output = result.get('StandardOutputContent', '')
                    if output:
                        print(f"输出: {output}")
                    return False
                elif status in ['InProgress', 'Pending']:
                    continue
                    
            except Exception as e:
                if 'InvocationDoesNotExist' in str(e):
                    continue
                else:
                    print(f"❌ 错误: {e}")
                    return False
        
        print("⚠️ 超时")
        return False
        
    except Exception as e:
        print(f"❌ 发送命令失败: {e}")
        return False

def main():
    try:
        # 读取部署信息
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        instance_id = deployment_info['instance_id']
        region = deployment_info['region']
        
        print(f"实例ID: {instance_id}")
        
        # 创建SSM客户端
        ssm = boto3.client('ssm', region_name=region)
        
        # 检查IPSec服务状态
        send_command(ssm, instance_id, [
            'systemctl status ipsec --no-pager -l',
            'journalctl -xeu ipsec.service --no-pager'
        ], "检查IPSec服务详细状态")
        
        # 检查配置文件
        send_command(ssm, instance_id, [
            'cat /etc/ipsec.conf',
            'cat /etc/ipsec.secrets'
        ], "检查配置文件")
        
        # 检查libreswan版本和功能
        send_command(ssm, instance_id, [
            'ipsec --version',
            'which ipsec',
            'ls -la /usr/sbin/ipsec',
            'ls -la /etc/ipsec.d/'
        ], "检查libreswan安装")
        
        # 尝试手动启动
        send_command(ssm, instance_id, [
            'ipsec setup start',
            'ipsec setup status'
        ], "尝试手动启动IPSec")
        
        # 检查网络配置
        send_command(ssm, instance_id, [
            'ip route show',
            'cat /proc/sys/net/ipv4/ip_forward'
        ], "检查网络配置")
        
        print("\n✅ IPSec调试完成！")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")

if __name__ == "__main__":
    print("=== IPSec调试工具 ===")
    main()
