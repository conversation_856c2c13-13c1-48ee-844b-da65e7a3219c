#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署配置文件 - 定义默认的AWS资源配置
确保使用最经济的资源配置
"""

# 默认实例配置
DEFAULT_INSTANCE_CONFIG = {
    'instance_type': 't3.micro',  # 默认使用最小的实例类型
    'min_count': 1,
    'max_count': 1,
    'key_name': 'Google-VPN-Key',
    'monitoring': False,  # 禁用详细监控以节省费用
    'ebs_optimized': False,  # t3.micro不支持EBS优化
    'block_device_mappings': [
        {
            'DeviceName': '/dev/xvda',
            'Ebs': {
                'VolumeSize': 8,  # 最小8GB存储
                'VolumeType': 'gp3',  # 使用gp3更经济
                'DeleteOnTermination': True,
                'Encrypted': False  # 禁用加密以节省费用
            }
        }
    ]
}

# 资源复用策略
RESOURCE_REUSE_POLICY = {
    'max_instances': 1,  # 最多保留1个实例
    'prefer_running': True,  # 优先使用运行中的实例
    'auto_cleanup': True,  # 自动清理无用资源
    'cost_optimization': True,  # 启用成本优化
    'force_reuse': True,  # 强制复用，禁止创建新实例
    'allowed_instance_types': ['t3.micro', 't3.nano'],  # 只允许最小的实例类型
}

# VPN服务配置
VPN_SERVICE_CONFIG = {
    'protocols': ['ikev2', 'l2tp'],
    'psk': 'GoogleVPN2023!',
    'users': {
        'vpnuser': 'GoogleVPN2023!',
        'testuser': 'GoogleVPN2023!',
        'admin': 'GoogleVPN2023!'
    },
    'dns_servers': ['*******', '*******'],
    'client_subnets': {
        'ikev2': '**********/24',
        'l2tp': '**********/24'
    }
}

# 网络配置
NETWORK_CONFIG = {
    'use_default_vpc': True,  # 使用默认VPC以节省费用
    'create_new_vpc': False,  # 禁止创建新VPC
    'security_group_rules': [
        {
            'IpProtocol': 'tcp',
            'FromPort': 22,
            'ToPort': 22,
            'IpRanges': [{'CidrIp': '0.0.0.0/0', 'Description': 'SSH'}]
        },
        {
            'IpProtocol': 'udp',
            'FromPort': 500,
            'ToPort': 500,
            'IpRanges': [{'CidrIp': '0.0.0.0/0', 'Description': 'IPSec IKE'}]
        },
        {
            'IpProtocol': 'udp',
            'FromPort': 4500,
            'ToPort': 4500,
            'IpRanges': [{'CidrIp': '0.0.0.0/0', 'Description': 'IPSec NAT-T'}]
        },
        {
            'IpProtocol': 'udp',
            'FromPort': 1701,
            'ToPort': 1701,
            'IpRanges': [{'CidrIp': '0.0.0.0/0', 'Description': 'L2TP'}]
        }
    ]
}

# 成本估算配置
COST_CONFIG = {
    't3.nano': 0.0052 * 24 * 30,    # 约$3.74/月
    't3.micro': 0.0104 * 24 * 30,   # 约$7.49/月
    't3.small': 0.0208 * 24 * 30,   # 约$14.98/月
    't3.medium': 0.0416 * 24 * 30,  # 约$29.95/月
    'eip': 3.6,  # $3.6/月
    'storage_gp3': 0.08,  # $0.08/GB/月
    'storage_gp2': 0.10,  # $0.10/GB/月
}

# 部署标签
DEPLOYMENT_TAGS = [
    {'Key': 'Project', 'Value': 'Network-API'},
    {'Key': 'Type', 'Value': 'VPN-Server'},
    {'Key': 'CostOptimized', 'Value': 'true'},
    {'Key': 'AutoManaged', 'Value': 'true'}
]

def get_instance_cost_per_month(instance_type):
    """获取实例类型的月度费用"""
    return COST_CONFIG.get(instance_type, 0)

def validate_instance_type(instance_type):
    """验证实例类型是否被允许"""
    return instance_type in RESOURCE_REUSE_POLICY['allowed_instance_types']

def get_recommended_instance_type():
    """获取推荐的实例类型"""
    return DEFAULT_INSTANCE_CONFIG['instance_type']

def should_force_reuse():
    """是否应该强制复用资源"""
    return RESOURCE_REUSE_POLICY['force_reuse']

def get_max_instances():
    """获取最大实例数量限制"""
    return RESOURCE_REUSE_POLICY['max_instances']

if __name__ == "__main__":
    print("=== 部署配置信息 ===")
    print(f"默认实例类型: {DEFAULT_INSTANCE_CONFIG['instance_type']}")
    print(f"月度费用: ${get_instance_cost_per_month(DEFAULT_INSTANCE_CONFIG['instance_type']):.2f}")
    print(f"强制复用: {should_force_reuse()}")
    print(f"最大实例数: {get_max_instances()}")
    print(f"允许的实例类型: {RESOURCE_REUSE_POLICY['allowed_instance_types']}")
