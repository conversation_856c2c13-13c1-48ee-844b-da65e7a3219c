#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通过AWS Session Manager检查VPN服务器状态
"""

import boto3
import json
import time
import subprocess
from botocore.exceptions import ClientError

def load_deployment_info():
    """加载部署信息"""
    try:
        with open('vpn_deployment_info.json', 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载部署信息失败: {e}")
        return None

def run_ssm_command(ssm_client, instance_id, commands, description=""):
    """通过SSM运行命令"""
    try:
        print(f"\n🔍 {description}")
        print(f"执行命令: {' && '.join(commands)}")
        
        response = ssm_client.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={'commands': commands},
            TimeoutSeconds=30
        )
        
        command_id = response['Command']['CommandId']
        
        # 等待命令执行完成
        for i in range(10):  # 最多等待10秒
            time.sleep(1)
            try:
                result = ssm_client.get_command_invocation(
                    CommandId=command_id,
                    InstanceId=instance_id
                )
                
                status = result['Status']
                if status == 'Success':
                    output = result['StandardOutputContent']
                    if output.strip():
                        print(f"✅ 输出:\n{output}")
                    else:
                        print("✅ 命令执行成功（无输出）")
                    return True
                elif status == 'Failed':
                    error = result['StandardErrorContent']
                    print(f"❌ 执行失败:\n{error}")
                    return False
                elif status in ['InProgress', 'Pending']:
                    continue
                else:
                    print(f"⚠️ 状态: {status}")
                    return False
                    
            except ClientError as e:
                if 'InvocationDoesNotExist' in str(e):
                    continue
                else:
                    print(f"❌ 获取命令结果失败: {e}")
                    return False
        
        print("⚠️ 命令执行超时")
        return False
        
    except Exception as e:
        print(f"❌ 执行SSM命令失败: {e}")
        return False

def check_ssh_service(ssm_client, instance_id):
    """检查SSH服务状态"""
    commands = [
        "systemctl is-active sshd",
        "systemctl is-enabled sshd",
        "ss -tlnp | grep :22",
        "cat /etc/ssh/sshd_config | grep -E '^(Port|PasswordAuthentication|PubkeyAuthentication)'"
    ]
    return run_ssm_command(ssm_client, instance_id, commands, "检查SSH服务状态")

def check_vpn_services(ssm_client, instance_id):
    """检查VPN服务状态"""
    commands = [
        "systemctl is-active ipsec",
        "systemctl is-active xl2tpd",
        "ipsec status",
        "ss -unp | grep -E ':(500|1701|4500)'"
    ]
    return run_ssm_command(ssm_client, instance_id, commands, "检查VPN服务状态")

def check_firewall_status(ssm_client, instance_id):
    """检查防火墙状态"""
    commands = [
        "systemctl is-active firewalld || echo 'firewalld not active'",
        "iptables -L INPUT -n | head -20",
        "netstat -tlnp | grep -E ':(22|500|1701|4500)'"
    ]
    return run_ssm_command(ssm_client, instance_id, commands, "检查防火墙和端口状态")

def fix_ssh_service(ssm_client, instance_id):
    """修复SSH服务"""
    commands = [
        "systemctl enable sshd",
        "systemctl start sshd",
        "systemctl status sshd --no-pager"
    ]
    return run_ssm_command(ssm_client, instance_id, commands, "修复SSH服务")

def restart_vpn_services(ssm_client, instance_id):
    """重启VPN服务"""
    commands = [
        "systemctl restart ipsec",
        "systemctl restart xl2tpd",
        "systemctl status ipsec --no-pager",
        "systemctl status xl2tpd --no-pager"
    ]
    return run_ssm_command(ssm_client, instance_id, commands, "重启VPN服务")

def get_server_public_ip(ssm_client, instance_id):
    """获取服务器的公网IP"""
    commands = [
        "curl -s http://***************/latest/meta-data/public-ipv4"
    ]
    return run_ssm_command(ssm_client, instance_id, commands, "获取服务器公网IP")

def test_ssh_connection_direct():
    """直接测试SSH连接"""
    try:
        deployment_info = load_deployment_info()
        if not deployment_info:
            return False
        
        public_ip = deployment_info['public_ip']
        print(f"\n🔍 测试SSH连接到 {public_ip}")
        
        # 测试SSH连接
        result = subprocess.run([
            'ssh', '-i', 'Google-VPN-Key.pem',
            '-o', 'StrictHostKeyChecking=no',
            '-o', 'ConnectTimeout=10',
            f'ec2-user@{public_ip}',
            'echo "SSH连接测试成功"'
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ SSH连接成功")
            return True
        else:
            print(f"❌ SSH连接失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ SSH连接测试失败: {e}")
        return False

def main():
    print("=== VPN服务器状态检查和修复工具 ===")
    
    # 1. 加载部署信息
    deployment_info = load_deployment_info()
    if not deployment_info:
        return
    
    instance_id = deployment_info['instance_id']
    region = deployment_info['region']
    
    print(f"实例ID: {instance_id}")
    print(f"地区: {region}")
    print(f"公网IP: {deployment_info['public_ip']}")
    
    # 2. 初始化SSM客户端
    try:
        ssm_client = boto3.client('ssm', region_name=region)
    except Exception as e:
        print(f"❌ 初始化SSM客户端失败: {e}")
        return
    
    # 3. 检查服务状态
    print(f"\n{'='*50}")
    print("开始检查服务器状态...")
    print(f"{'='*50}")
    
    # 获取服务器IP
    get_server_public_ip(ssm_client, instance_id)
    
    # 检查SSH服务
    ssh_ok = check_ssh_service(ssm_client, instance_id)
    
    # 检查VPN服务
    vpn_ok = check_vpn_services(ssm_client, instance_id)
    
    # 检查防火墙
    firewall_ok = check_firewall_status(ssm_client, instance_id)
    
    # 4. 修复问题
    print(f"\n{'='*50}")
    print("开始修复问题...")
    print(f"{'='*50}")
    
    if not ssh_ok:
        print("\n🔧 修复SSH服务...")
        fix_ssh_service(ssm_client, instance_id)
    
    if not vpn_ok:
        print("\n🔧 重启VPN服务...")
        restart_vpn_services(ssm_client, instance_id)
    
    # 5. 测试连接
    print(f"\n{'='*50}")
    print("测试连接...")
    print(f"{'='*50}")
    
    # 直接测试SSH连接
    test_ssh_connection_direct()
    
    print(f"\n✅ 服务器状态检查完成")
    print(f"请运行 'python test_vpn_connections.py' 进行VPN连接测试")

if __name__ == "__main__":
    main()
