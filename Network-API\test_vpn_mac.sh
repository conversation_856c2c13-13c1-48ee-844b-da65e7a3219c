#!/bin/bash
# Mac VPN连接测试脚本

SERVER_IP="*************"
VPN_NAME="Google-VPN-Test"
USERNAME="vpnuser"
PASSWORD="GoogleVPN2023!"
PSK="GoogleVPN2023!"

echo "=== Mac VPN连接测试工具 ==="
echo "服务器: $SERVER_IP"
echo "测试时间: $(date)"
echo ""

# 检查管理员权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 需要管理员权限运行此脚本"
    echo "请使用: sudo $0"
    exit 1
fi

# 函数：测试端口连通性
test_ports() {
    echo "=== 测试端口连通性 ==="
    
    ports=(500 4500 1701)
    port_names=("IPSec IKE" "IPSec NAT-T" "L2TP")
    
    for i in "${!ports[@]}"; do
        port=${ports[$i]}
        name=${port_names[$i]}
        
        if nc -u -z -w3 $SERVER_IP $port 2>/dev/null; then
            echo "✅ $name (UDP/$port) - 可达"
        else
            echo "❌ $name (UDP/$port) - 不可达"
        fi
    done
    echo ""
}

# 函数：创建VPN配置
create_vpn_config() {
    echo "=== 创建VPN配置 ==="
    
    # 删除已存在的配置
    scutil --nc remove "$VPN_NAME" 2>/dev/null || true
    
    # 创建临时配置文件
    cat > /tmp/vpn_config.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>UserDefinedName</key>
    <string>$VPN_NAME</string>
    <key>VPN</key>
    <dict>
        <key>AuthName</key>
        <string>$USERNAME</string>
        <key>AuthPassword</key>
        <string>$PASSWORD</string>
        <key>RemoteAddress</key>
        <string>$SERVER_IP</string>
        <key>SharedSecret</key>
        <string>$PSK</string>
        <key>SubType</key>
        <string>IKEv2</string>
    </dict>
</dict>
</plist>
EOF
    
    # 导入配置
    if scutil --nc add "$VPN_NAME" --file /tmp/vpn_config.plist; then
        echo "✅ VPN配置已创建"
        rm -f /tmp/vpn_config.plist
        return 0
    else
        echo "❌ VPN配置创建失败"
        rm -f /tmp/vpn_config.plist
        return 1
    fi
}

# 函数：测试VPN连接
test_vpn_connection() {
    echo "=== 测试VPN连接 ==="
    
    echo "🔄 尝试连接VPN..."
    
    # 连接VPN
    if scutil --nc start "$VPN_NAME"; then
        echo "✅ VPN连接命令已发送"
        
        # 等待连接建立
        echo "⏳ 等待连接建立..."
        sleep 10
        
        # 检查连接状态
        status=$(scutil --nc status "$VPN_NAME")
        echo "连接状态: $status"
        
        if echo "$status" | grep -q "Connected"; then
            echo "✅ VPN连接成功！"
            
            # 测试IP地址
            test_ip_address
            
            # 断开连接
            echo "🔄 断开VPN连接..."
            scutil --nc stop "$VPN_NAME"
            sleep 3
            
            return 0
        else
            echo "❌ VPN连接失败"
            return 1
        fi
    else
        echo "❌ VPN连接命令失败"
        return 1
    fi
}

# 函数：测试IP地址
test_ip_address() {
    echo "🔍 检查公网IP地址..."
    
    # 获取当前IP
    current_ip=$(curl -s --connect-timeout 10 https://api.ipify.org)
    
    if [ -n "$current_ip" ]; then
        echo "当前公网IP: $current_ip"
        
        if [ "$current_ip" = "$SERVER_IP" ]; then
            echo "✅ IP地址正确，VPN连接有效"
        else
            echo "⚠️ IP地址不匹配，可能VPN未生效"
        fi
        
        # 测试Google访问
        echo "🔍 测试Google服务访问..."
        if curl -s --connect-timeout 10 https://www.google.com > /dev/null; then
            echo "✅ Google访问正常"
        else
            echo "❌ Google访问失败"
        fi
    else
        echo "❌ 无法获取IP地址"
    fi
}

# 函数：清理配置
cleanup() {
    echo "=== 清理测试配置 ==="
    
    # 断开连接
    scutil --nc stop "$VPN_NAME" 2>/dev/null || true
    
    # 删除配置
    if scutil --nc remove "$VPN_NAME" 2>/dev/null; then
        echo "🗑️ 已删除测试配置: $VPN_NAME"
    fi
}

# 主测试流程
echo "开始Mac VPN连接测试..."
echo ""

# 1. 测试端口连通性
test_ports

# 2. 创建VPN配置
if create_vpn_config; then
    # 3. 测试VPN连接
    connection_result=$(test_vpn_connection && echo "success" || echo "failed")
else
    connection_result="config_failed"
fi

# 4. 清理配置
cleanup

# 5. 总结结果
echo ""
echo "=================================================="
echo "测试结果总结"
echo "=================================================="
echo ""

echo "📊 Mac VPN连接测试结果:"
case $connection_result in
    "success")
        echo "✅ IKEv2连接: 成功"
        echo "✅ Mac系统兼容性: 良好"
        ;;
    "failed")
        echo "❌ IKEv2连接: 失败"
        echo "⚠️ 可能需要调整服务器配置"
        ;;
    "config_failed")
        echo "❌ 配置创建: 失败"
        echo "⚠️ 请检查系统权限"
        ;;
esac

echo ""
echo "📋 手动配置信息:"
echo "服务器地址: $SERVER_IP"
echo "VPN类型: IKEv2"
echo "用户名: $USERNAME"
echo "密码: $PASSWORD"
echo "共享密钥: $PSK"

echo ""
echo "🔗 手动配置步骤:"
echo "1. 系统偏好设置 → 网络"
echo "2. 点击 '+' → 接口: VPN → VPN类型: IKEv2"
echo "3. 输入上述配置信息"
echo "4. 点击连接"

echo ""
echo "测试完成！"
