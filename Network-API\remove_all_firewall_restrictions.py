#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彻底解除防火墙限制脚本
完全开放所有端口和协议，确保VPN客户端不受任何限制
"""

import boto3
import json
import os
import sys
import time
import subprocess
import base64
from botocore.exceptions import ClientError

def load_deployment_info():
    """加载部署信息"""
    try:
        with open('vpn_deployment_info.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 错误：未找到部署信息文件")
        return None

def create_no_firewall_script():
    """创建完全无防火墙限制的脚本"""
    script = '''#!/bin/bash
set -e
exec > >(tee /var/log/no-firewall.log|logger -t no-firewall -s 2>/dev/console) 2>&1

echo "=== 彻底解除防火墙限制脚本 ==="
echo "开始时间: $(date)"
echo "目标: 完全开放所有端口，不阻止任何VPN客户端访问"

# 1. 停止并禁用所有安全服务
echo "1. 停止所有安全服务..."
systemctl stop fail2ban 2>/dev/null || true
systemctl disable fail2ban 2>/dev/null || true
systemctl stop firewalld 2>/dev/null || true
systemctl disable firewalld 2>/dev/null || true
systemctl stop ufw 2>/dev/null || true
systemctl disable ufw 2>/dev/null || true

# 杀死所有可能的安全进程
pkill -f "fail2ban" 2>/dev/null || true
pkill -f "denyhosts" 2>/dev/null || true
pkill -f "csf" 2>/dev/null || true

# 2. 完全清空并禁用iptables
echo "2. 完全清空iptables规则..."
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X
iptables -t mangle -F
iptables -t mangle -X
iptables -t raw -F
iptables -t raw -X
iptables -t security -F 2>/dev/null || true
iptables -t security -X 2>/dev/null || true

# 3. 设置最宽松的默认策略
echo "3. 设置完全开放的默认策略..."
iptables -P INPUT ACCEPT
iptables -P FORWARD ACCEPT
iptables -P OUTPUT ACCEPT

# 4. 只添加必要的NAT规则（VPN客户端上网必需）
echo "4. 添加VPN NAT规则..."
iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE
iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE
iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE

# 5. 系统参数优化
echo "5. 优化系统参数..."
cat > /etc/sysctl.conf << 'EOF'
# 完全开放的网络配置
net.ipv4.ip_forward = 1
net.ipv6.conf.all.forwarding = 1
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.all.rp_filter = 0
net.ipv4.conf.eth0.rp_filter = 0

# 网络性能优化
net.core.rmem_max = *********
net.core.wmem_max = *********
net.ipv4.tcp_rmem = 4096 65536 *********
net.ipv4.tcp_wmem = 4096 65536 *********

# 连接优化
net.ipv4.tcp_keepalive_time = 600
net.ipv4.tcp_keepalive_intvl = 60
net.ipv4.tcp_keepalive_probes = 3

# 连接跟踪优化
net.netfilter.nf_conntrack_max = 1048576
net.netfilter.nf_conntrack_tcp_timeout_established = 7200
EOF

sysctl -p

# 6. SSH服务配置（最宽松）
echo "6. 配置最宽松的SSH服务..."
cat > /etc/ssh/sshd_config << 'EOF'
Port 22
Protocol 2
PermitRootLogin no
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
PasswordAuthentication no
ChallengeResponseAuthentication no
UsePAM yes

# 最宽松的连接设置
ClientAliveInterval 30
ClientAliveCountMax 20
TCPKeepAlive yes
MaxAuthTries 20
MaxSessions 50
MaxStartups 50:30:100
LoginGraceTime 600

# 性能优化
Compression yes
UseDNS no
X11Forwarding no
PrintMotd no
AllowUsers ec2-user
EOF

systemctl restart sshd
systemctl enable sshd

# 7. VPN服务配置
echo "7. 配置VPN服务..."

# StrongSwan配置
cat > /etc/strongswan/strongswan.conf << 'EOF'
charon {
    load_modular = yes
    plugins {
        include strongswan.d/charon/*.conf
    }
    threads = 16
}
include strongswan.d/*.conf
EOF

cat > /etc/strongswan/ipsec.conf << 'EOF'
config setup
    charondebug="ike 1, knl 1, cfg 0"
    uniqueids=no

conn %default
    left=%defaultroute
    leftsubnet=0.0.0.0/0
    right=%any
    rightsourceip=**********/24
    rightdns=*******,*******
    auto=add
    keyexchange=ikev2
    ike=aes256-sha256-modp2048,aes256-sha1-modp2048,aes128-sha256-modp2048,aes128-sha1-modp2048!
    esp=aes256-sha256,aes256-sha1,aes128-sha256,aes128-sha1!
    dpdaction=clear
    dpddelay=300s
    rekey=no
    leftfirewall=yes

conn Google-VPN-EAP
    also=%default
    leftauth=psk
    rightauth=eap-mschapv2
    rightsubnet=0.0.0.0/0
    leftid=************
    rightid=%any
    eap_identity=%identity

conn Google-VPN-PSK
    also=%default
    leftauth=psk
    rightauth=psk
    rightsubnet=0.0.0.0/0
    leftid=************
    rightid=%any

conn L2TP-PSK
    also=%default
    leftauth=psk
    rightauth=psk
    type=transport
    leftprotoport=17/1701
    rightprotoport=17/%any
    rightsubnet=vhost:%priv
    leftid=************
    rightid=%any
EOF

cat > /etc/strongswan/ipsec.secrets << 'EOF'
: PSK "GoogleVPN2023!"
************ %any : PSK "GoogleVPN2023!"
vpnuser : EAP "GoogleVPN2023!"
testuser : EAP "GoogleVPN2023!"
admin : EAP "GoogleVPN2023!"
EOF

# L2TP配置
cat > /etc/xl2tpd/xl2tpd.conf << 'EOF'
[global]
listen-addr = 0.0.0.0
port = 1701
auth file = /etc/ppp/chap-secrets

[lns default]
ip range = ***********-************
local ip = **********
require chap = yes
refuse pap = yes
require authentication = yes
name = l2tpd
ppp debug = yes
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
EOF

cat > /etc/ppp/options.xl2tpd << 'EOF'
ipcp-accept-local
ipcp-accept-remote
ms-dns *******
ms-dns *******
noccp
auth
crtscts
idle 1800
mtu 1410
mru 1410
nodefaultroute
debug
lock
proxyarp
connect-delay 5000
require-mschap-v2
EOF

cat > /etc/ppp/chap-secrets << 'EOF'
vpnuser l2tpd GoogleVPN2023! *
testuser l2tpd GoogleVPN2023! *
admin l2tpd GoogleVPN2023! *
EOF

# 8. 启动VPN服务
echo "8. 启动VPN服务..."
systemctl enable strongswan
systemctl restart strongswan
systemctl enable xl2tpd
systemctl restart xl2tpd

# 9. 创建开机自启脚本（确保重启后保持无防火墙状态）
echo "9. 创建开机自启脚本..."
cat > /etc/systemd/system/no-firewall.service << 'EOF'
[Unit]
Description=Disable all firewall restrictions
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'iptables -P INPUT ACCEPT; iptables -P FORWARD ACCEPT; iptables -P OUTPUT ACCEPT; iptables -F; iptables -X; iptables -t nat -F; iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE; iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE; iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE'
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

systemctl enable no-firewall

# 10. 创建监控脚本（确保防火墙始终保持开放）
echo "10. 创建防火墙监控脚本..."
cat > /usr/local/bin/ensure-no-firewall.sh << 'EOF'
#!/bin/bash
# 确保防火墙始终保持完全开放状态

LOG_FILE="/var/log/no-firewall-monitor.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> $LOG_FILE
}

# 检查并确保默认策略为ACCEPT
for chain in INPUT FORWARD OUTPUT; do
    current_policy=$(iptables -L $chain | head -1 | grep -o "policy [A-Z]*" | cut -d' ' -f2)
    if [ "$current_policy" != "ACCEPT" ]; then
        log_message "检测到$chain策略不是ACCEPT，正在修复..."
        iptables -P $chain ACCEPT
    fi
done

# 检查是否有限制性规则
rule_count=$(iptables -L INPUT | wc -l)
if [ $rule_count -gt 3 ]; then
    log_message "检测到INPUT链有限制性规则，正在清理..."
    iptables -F INPUT
fi

# 确保NAT规则存在
if ! iptables -t nat -L POSTROUTING | grep -q "**********/24"; then
    log_message "NAT规则缺失，正在添加..."
    iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE
    iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE
fi

# 检查VPN服务状态
for service in sshd strongswan xl2tpd; do
    if ! systemctl is-active --quiet $service; then
        log_message "$service 服务异常，重启中..."
        systemctl restart $service
    fi
done

log_message "防火墙监控检查完成 - 状态: 完全开放"
EOF

chmod +x /usr/local/bin/ensure-no-firewall.sh

# 添加到定时任务（每分钟检查一次）
cat > /etc/cron.d/no-firewall-monitor << 'EOF'
# 防火墙开放状态监控
* * * * * root /usr/local/bin/ensure-no-firewall.sh
EOF

# 11. 保存当前状态
echo "11. 保存当前状态..."
iptables-save > /etc/iptables-open.rules

# 12. 最终验证
echo "12. 最终验证..."
echo "当前iptables策略:"
iptables -L | head -3

echo "NAT规则:"
iptables -t nat -L POSTROUTING

echo "监听端口:"
ss -tlnp | grep -E ":22|:500|:4500|:1701"

echo "VPN服务状态:"
systemctl is-active sshd strongswan xl2tpd

echo "=== 防火墙限制完全解除完成 ==="
echo "完成时间: $(date)"

# 创建完成标记
cat > /home/<USER>/no-firewall-complete.txt << 'EOF'
防火墙限制完全解除完成
====================

完成时间: $(date)
服务器IP: ************

解除的限制:
1. 所有iptables INPUT规则 - 完全清空
2. 所有安全服务 - 完全禁用
3. 默认策略 - 设为ACCEPT
4. 端口限制 - 完全开放
5. 协议限制 - 完全开放

安全策略:
- 完全无防火墙限制
- 所有端口完全开放
- 所有协议完全开放
- VPN客户端无任何访问限制
- 自动监控确保始终保持开放状态

VPN连接信息:
- 服务器: ************
- 用户名: vpnuser
- 密码: GoogleVPN2023!
- 预共享密钥: GoogleVPN2023!

现在VPN客户端可以无限制访问，不会被任何防火墙规则阻止！
监控日志: /var/log/no-firewall-monitor.log
EOF

chown ec2-user:ec2-user /home/<USER>/no-firewall-complete.txt
'''
    return script

def execute_no_firewall_fix(deployment_info):
    """执行完全解除防火墙限制"""
    print("=== 执行完全解除防火墙限制 ===")

    try:
        ec2 = boto3.client('ec2', region_name=deployment_info['region'])
        instance_id = deployment_info['instance_id']

        # 首先修复安全组，确保所有端口开放
        print("🔄 修复安全组配置...")
        sg_id = deployment_info['security_group_id']

        # 获取当前安全组规则
        response = ec2.describe_security_groups(GroupIds=[sg_id])
        sg = response['SecurityGroups'][0]

        # 删除所有现有规则
        if sg['IpPermissions']:
            try:
                ec2.revoke_security_group_ingress(
                    GroupId=sg_id,
                    IpPermissions=sg['IpPermissions']
                )
                print("✅ 已清理现有安全组规则")
            except Exception as e:
                print(f"⚠️ 清理规则时出错: {e}")

        # 添加完全开放的规则
        open_rules = [
            {
                'IpProtocol': '-1',
                'IpRanges': [{'CidrIp': '0.0.0.0/0', 'Description': 'All traffic - no restrictions'}]
            }
        ]

        ec2.authorize_security_group_ingress(
            GroupId=sg_id,
            IpPermissions=open_rules
        )
        print("✅ 安全组已设置为完全开放")

        print("🔄 停止实例进行防火墙限制解除...")
        ec2.stop_instances(InstanceIds=[instance_id])

        # 等待实例停止
        waiter = ec2.get_waiter('instance_stopped')
        waiter.wait(InstanceIds=[instance_id])
        print("✅ 实例已停止")

        # 应用无防火墙脚本
        no_firewall_script = create_no_firewall_script()
        user_data_encoded = base64.b64encode(no_firewall_script.encode()).decode()

        print("🔄 应用完全解除防火墙限制脚本...")
        ec2.modify_instance_attribute(
            InstanceId=instance_id,
            UserData={'Value': user_data_encoded}
        )
        print("✅ 防火墙限制解除脚本已应用")

        # 启动实例
        print("🔄 启动实例...")
        ec2.start_instances(InstanceIds=[instance_id])

        # 等待实例运行
        waiter = ec2.get_waiter('instance_running')
        waiter.wait(InstanceIds=[instance_id])
        print("✅ 实例已启动")

        # 等待配置完成
        print("⏳ 等待防火墙限制解除完成（约3分钟）...")
        time.sleep(180)

        return True

    except Exception as e:
        print(f"❌ 防火墙限制解除失败: {e}")
        return False

def test_no_firewall_result(public_ip):
    """测试无防火墙限制的效果"""
    print(f"\n=== 测试无防火墙限制效果 ===")
    
    # SSH连接稳定性测试
    print("1. SSH连接稳定性测试（连续15次）...")
    success_count = 0
    
    for i in range(1, 16):
        try:
            result = subprocess.run([
                'ssh', '-i', 'Google-VPN-Key.pem',
                '-o', 'StrictHostKeyChecking=no',
                '-o', 'ConnectTimeout=8',
                f'ec2-user@{public_ip}',
                f'echo "无防火墙测试{i}成功" && date'
            ], capture_output=True, text=True, timeout=12)
            
            if result.returncode == 0:
                print(f"✅ 测试{i}: 成功")
                success_count += 1
            else:
                print(f"❌ 测试{i}: 失败")
        except:
            print(f"❌ 测试{i}: 超时")
        
        time.sleep(0.5)  # 快速连续测试
    
    ssh_success_rate = (success_count / 15) * 100
    print(f"\nSSH连接成功率: {ssh_success_rate}%")
    
    # 检查防火墙状态
    if success_count > 0:
        print("\n2. 检查防火墙状态...")
        try:
            result = subprocess.run([
                'ssh', '-i', 'Google-VPN-Key.pem',
                '-o', 'StrictHostKeyChecking=no',
                '-o', 'ConnectTimeout=10',
                f'ec2-user@{public_ip}',
                'sudo iptables -L | head -5 && echo "---NAT规则---" && sudo iptables -t nat -L POSTROUTING | head -5'
            ], capture_output=True, text=True, timeout=15)
            
            print("防火墙状态:")
            print(result.stdout)
        except:
            print("防火墙状态检查失败")
    
    return ssh_success_rate

def main():
    """主函数"""
    print("彻底解除防火墙限制工具")
    print("======================")
    print("完全开放所有端口和协议")
    print("确保VPN客户端不受任何防火墙限制")
    
    # 加载部署信息
    deployment_info = load_deployment_info()
    if not deployment_info:
        return
    
    public_ip = deployment_info['public_ip']
    print(f"\n目标服务器: {public_ip}")
    
    print(f"\n{'='*50}")
    print("解除限制方案")
    print(f"{'='*50}")
    print("1. 完全清空所有iptables规则")
    print("2. 设置默认策略为ACCEPT")
    print("3. 禁用所有安全服务（fail2ban等）")
    print("4. 只保留VPN必需的NAT规则")
    print("5. 部署监控确保始终保持开放状态")
    print("6. 每分钟自动检查并恢复开放状态")
    
    print(f"\n⚠️ 注意事项:")
    print("- 这将完全禁用服务器防火墙")
    print("- 所有端口将完全开放")
    print("- VPN客户端将不受任何限制")
    print("- 适合VPN专用服务器使用")
    
    choice = input(f"\n是否彻底解除防火墙限制？(y/N): ").strip().lower()
    
    if choice == 'y':
        print("\n开始彻底解除防火墙限制...")
        
        if execute_no_firewall_fix(deployment_info):
            print("\n✅ 防火墙限制解除完成！")
            
            # 测试效果
            print("\n开始测试解除效果...")
            ssh_rate = test_no_firewall_result(public_ip)
            
            print(f"\n{'='*50}")
            print("解除限制结果")
            print(f"{'='*50}")
            
            if ssh_rate >= 95:
                print(f"🎉 防火墙限制完全解除成功！")
                print(f"SSH连接成功率: {ssh_rate}%")
                print("VPN客户端现在可以无限制访问")
            else:
                print(f"⚠️ SSH连接成功率: {ssh_rate}%")
                print("可能还需要进一步优化")
            
            print(f"\n🔑 VPN连接信息:")
            print(f"服务器地址: {public_ip}")
            print(f"用户名: vpnuser")
            print(f"密码: GoogleVPN2023!")
            print(f"预共享密钥: GoogleVPN2023!")
            
            print(f"\n📱 支持的连接方式:")
            print(f"1. IKEv2 + EAP认证（用户名密码）")
            print(f"2. L2TP/IPSec")
            print(f"3. IKEv2 + PSK认证")
            
            print(f"\n🌐 验证方法:")
            print(f"连接VPN后访问: https://whatismyipaddress.com")
            print(f"应显示IP: {public_ip}")
            print(f"测试Google服务: https://www.google.com")
            
            if ssh_rate >= 95:
                print(f"\n🎯 状态: ✅ 完全成功")
                print("所有防火墙限制已彻底解除")
                print("VPN客户端可以无限制长期稳定使用")
            else:
                print(f"\n🎯 状态: ⚠️ 需要进一步验证")
        else:
            print("\n❌ 防火墙限制解除失败")
    else:
        print("取消操作")

if __name__ == "__main__":
    main()
