#!/usr/bin/env python3
"""
SSH和VPN连接问题的最终解决方案
彻底解决SSH连接失败的本质问题
"""

import boto3
import json
import base64
import time
import subprocess

def create_final_fix_script():
    """创建最终的SSH和VPN修复脚本"""
    script = '''#!/bin/bash
exec > /var/log/final-ssh-vpn-fix.log 2>&1
echo "=== 开始最终SSH和VPN修复 ==="
date

# 1. 基础系统配置
echo "1. 配置基础系统..."
yum update -y
amazon-linux-extras install -y epel
yum install -y net-tools curl wget htop vim

# 2. 确保SSH服务完全正常
echo "2. 修复SSH服务..."
systemctl stop sshd
systemctl disable sshd

# 重新安装SSH服务
yum reinstall -y openssh-server openssh-clients

# 创建标准SSH配置
cat > /etc/ssh/sshd_config << 'SSH_EOF'
Port 22
Protocol 2
PermitRootLogin no
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
PasswordAuthentication no
ChallengeResponseAuthentication no
UsePAM yes
ClientAliveInterval 60
ClientAliveCountMax 10
TCPKeepAlive yes
MaxAuthTries 10
MaxSessions 20
MaxStartups 20:30:60
LoginGraceTime 300
UseDNS no
AllowUsers ec2-user
SSH_EOF

# 3. 确保ec2-user配置正确
echo "3. 配置ec2-user..."
if ! id ec2-user &>/dev/null; then
    useradd -m -s /bin/bash ec2-user
fi

# 配置sudo权限
echo "ec2-user ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/90-ec2-user
chmod 440 /etc/sudoers.d/90-ec2-user

# 4. 配置SSH密钥
echo "4. 配置SSH密钥..."
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# 从元数据获取公钥并配置
curl -s http://***************/latest/meta-data/public-keys/0/openssh-key > /home/<USER>/.ssh/authorized_keys
chmod 600 /home/<USER>/.ssh/authorized_keys
chown -R ec2-user:ec2-user /home/<USER>/.ssh

# 5. 启动SSH服务
echo "5. 启动SSH服务..."
systemctl enable sshd
systemctl start sshd
systemctl status sshd

# 6. 配置防火墙
echo "6. 配置防火墙..."
systemctl stop firewalld 2>/dev/null || true
systemctl disable firewalld 2>/dev/null || true

# 清空并配置iptables
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X

# 设置开放策略
iptables -P INPUT ACCEPT
iptables -P FORWARD ACCEPT
iptables -P OUTPUT ACCEPT

# 基础规则
iptables -A INPUT -i lo -j ACCEPT
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# SSH端口（确保SSH可访问）
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# VPN端口
iptables -A INPUT -p udp --dport 500 -j ACCEPT
iptables -A INPUT -p udp --dport 4500 -j ACCEPT
iptables -A INPUT -p udp --dport 1701 -j ACCEPT
iptables -A INPUT -p 50 -j ACCEPT

# NAT规则
iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE

# 保存规则
iptables-save > /etc/iptables.rules

# 7. 安装和配置VPN服务
echo "7. 安装VPN服务..."
yum install -y strongswan xl2tpd ppp

# 配置StrongSwan
cat > /etc/strongswan/ipsec.conf << 'IPSEC_EOF'
config setup
    charondebug="ike 1, knl 1, cfg 0"
    uniqueids=no

conn %default
    left=%defaultroute
    leftsubnet=0.0.0.0/0
    right=%any
    rightsourceip=**********/24
    rightdns=*******,*******
    auto=add
    keyexchange=ikev2
    ike=aes256-sha256-modp2048!
    esp=aes256-sha256!
    dpdaction=clear
    dpddelay=300s
    rekey=no

conn Google-VPN
    also=%default
    leftauth=psk
    rightauth=eap-mschapv2
    eap_identity=%identity
IPSEC_EOF

cat > /etc/strongswan/ipsec.secrets << 'SECRETS_EOF'
: PSK "GoogleVPN2023!"
vpnuser : EAP "GoogleVPN2023!"
testuser : EAP "GoogleVPN2023!"
admin : EAP "GoogleVPN2023!"
SECRETS_EOF

# 8. 启动所有服务
echo "8. 启动所有服务..."
systemctl enable strongswan
systemctl start strongswan
systemctl enable xl2tpd
systemctl start xl2tpd

# 9. 验证服务状态
echo "9. 验证服务状态..."
echo "SSH服务状态:"
systemctl status sshd --no-pager
echo "StrongSwan状态:"
systemctl status strongswan --no-pager
echo "XL2TPD状态:"
systemctl status xl2tpd --no-pager

# 10. 网络配置验证
echo "10. 网络配置验证..."
echo "网络接口:"
ip addr show
echo "路由表:"
ip route show
echo "防火墙规则:"
iptables -L -n

echo "=== SSH和VPN修复完成 ==="
date
echo "SSH端口22已开放，VPN服务已启动"
'''
    return script

def main():
    print("=== SSH连接问题最终解决方案 ===")
    
    # 加载部署信息
    try:
        with open('vpn_deployment_info.json', 'r') as f:
            info = json.load(f)
    except FileNotFoundError:
        print("❌ 找不到部署信息文件")
        return False
    
    ec2 = boto3.client('ec2', region_name=info['region'])
    
    print(f"目标实例: {info['instance_id']}")
    print(f"目标IP: {info['public_ip']}")
    
    # 创建修复脚本
    fix_script = create_final_fix_script()
    encoded_script = base64.b64encode(fix_script.encode()).decode()
    
    try:
        # 停止实例
        print("🔄 停止实例以应用最终修复...")
        ec2.stop_instances(InstanceIds=[info['instance_id']])
        
        # 等待停止
        waiter = ec2.get_waiter('instance_stopped')
        waiter.wait(InstanceIds=[info['instance_id']])
        print("✅ 实例已停止")
        
        # 应用修复脚本
        print("🔄 应用最终SSH和VPN修复脚本...")
        ec2.modify_instance_attribute(
            InstanceId=info['instance_id'],
            UserData={'Value': encoded_script}
        )
        
        # 启动实例
        print("🔄 启动实例...")
        ec2.start_instances(InstanceIds=[info['instance_id']])
        
        # 等待运行
        waiter = ec2.get_waiter('instance_running')
        waiter.wait(InstanceIds=[info['instance_id']])
        print("✅ 实例已启动")
        
        # 等待服务完全启动
        print("⏳ 等待SSH和VPN服务完全启动（180秒）...")
        time.sleep(180)
        
        # 测试SSH连接
        print("\n=== 测试SSH连接 ===")
        ssh_cmd = f'ssh -i Google-VPN-Key.pem -o StrictHostKeyChecking=no -o ConnectTimeout=15 ec2-user@{info["public_ip"]} "echo \'SSH连接成功\' && date && whoami"'
        
        result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True, timeout=20)
        
        if result.returncode == 0:
            print("🎉 SSH连接修复成功！")
            print("输出:")
            print(result.stdout)
            
            print("\n✅ SSH连接问题已彻底解决")
            print("✅ VPN客户端现在可以通过SSH端口正常访问")
            return True
        else:
            print("❌ SSH连接仍然失败")
            print(f"错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 修复过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 SSH连接问题已彻底解决！")
        print("VPN客户端现在可以正常通过SSH端口访问服务器")
    else:
        print("\n❌ 需要进一步诊断SSH问题")
