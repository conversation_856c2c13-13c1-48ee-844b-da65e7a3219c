#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN管理工具 - 统一管理脚本
整合部署、检查、配置等所有功能
"""

import os
import sys
import subprocess
import json

def show_menu():
    """显示主菜单"""
    print("\n" + "="*50)
    print("🚀 Network-API VPN 管理工具")
    print("="*50)
    print("1. 部署/重新部署VPN服务器")
    print("2. 检查VPN服务状态")
    print("3. 检查AWS资源状态")
    print("4. 生成客户端配置指南")
    print("5. 测试Google服务访问")
    print("6. 查看部署信息")
    print("7. 退出")
    print("="*50)

def run_script(script_name, description):
    """运行指定脚本"""
    print(f"\n🔄 {description}...")
    try:
        if script_name.endswith('.py'):
            result = subprocess.run([sys.executable, script_name], 
                                  cwd=os.path.dirname(os.path.abspath(__file__)),
                                  check=True)
        else:
            result = subprocess.run([script_name], 
                                  cwd=os.path.dirname(os.path.abspath(__file__)),
                                  check=True, shell=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        return False
    except FileNotFoundError:
        print(f"❌ 找不到脚本文件: {script_name}")
        return False

def show_deployment_info():
    """显示部署信息"""
    try:
        with open('vpn_deployment_info.json', 'r') as f:
            info = json.load(f)
        
        print("\n📋 当前VPN部署信息:")
        print("="*30)
        print(f"实例ID: {info['instance_id']}")
        print(f"公网IP: {info['public_ip']}")
        print(f"VPC ID: {info['vpc_id']}")
        print(f"子网ID: {info['subnet_id']}")
        print(f"安全组ID: {info['security_group_id']}")
        print(f"区域: {info['region']}")
        
        # 显示连接信息
        try:
            import configparser
            config = configparser.ConfigParser()
            if os.path.exists('vpn_config.ini'):
                config.read('vpn_config.ini')
                psk = config.get('VPN', 'PreSharedKey', fallback='GoogleVPN2023!')
            else:
                psk = 'GoogleVPN2023!'
        except:
            psk = 'GoogleVPN2023!'
        
        print(f"\n🔑 连接信息:")
        print(f"服务器地址: {info['public_ip']}")
        print(f"用户名: vpnuser")
        print(f"密码: {psk}")
        print(f"预共享密钥: {psk}")
        
    except FileNotFoundError:
        print("❌ 未找到部署信息文件，请先部署VPN服务器")
    except Exception as e:
        print(f"❌ 读取部署信息失败: {e}")

def test_google_access():
    """测试Google服务访问"""
    print("\n🌐 测试Google服务访问...")
    
    try:
        with open('vpn_deployment_info.json', 'r') as f:
            info = json.load(f)
        public_ip = info['public_ip']
        
        # 通过SSH在服务器上测试
        test_script = '''
echo "测试Google服务访问..."
echo "1. 测试DNS解析:"
nslookup www.google.com
nslookup aistudio.google.com

echo "2. 测试网络连通性:"
ping -c 2 www.google.com
ping -c 2 aistudio.google.com

echo "3. 测试HTTP访问:"
curl -s -o /dev/null -w "Google搜索: %{http_code}\\n" --max-time 10 https://www.google.com
curl -s -o /dev/null -w "Google AI Studio: %{http_code}\\n" --max-time 10 https://aistudio.google.com
curl -s -o /dev/null -w "YouTube: %{http_code}\\n" --max-time 10 https://www.youtube.com

echo "测试完成"
'''
        
        # 执行测试
        result = subprocess.run([
            'ssh', '-i', 'Google-VPN-Key.pem', '-o', 'StrictHostKeyChecking=no',
            f'ec2-user@{public_ip}', test_script
        ], capture_output=True, text=True, timeout=60)
        
        print("测试结果:")
        print(result.stdout)
        if result.stderr:
            print("警告信息:")
            print(result.stderr)
            
    except FileNotFoundError:
        print("❌ 未找到部署信息文件")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def generate_client_config():
    """生成客户端配置指南"""
    print("\n📱 生成客户端配置指南...")
    
    try:
        with open('vpn_deployment_info.json', 'r') as f:
            info = json.load(f)
        
        try:
            import configparser
            config = configparser.ConfigParser()
            if os.path.exists('vpn_config.ini'):
                config.read('vpn_config.ini')
                psk = config.get('VPN', 'PreSharedKey', fallback='GoogleVPN2023!')
            else:
                psk = 'GoogleVPN2023!'
        except:
            psk = 'GoogleVPN2023!'
        
        public_ip = info['public_ip']
        
        config_content = f"""# VPN客户端配置指南

## 🌐 服务器信息
- **服务器地址**: {public_ip}
- **预共享密钥**: {psk}
- **用户名**: vpnuser (或 testuser, admin)
- **密码**: {psk}

## 💻 Windows 连接方法

### 方法1: IKEv2 (推荐)
1. 设置 → 网络和Internet → VPN → 添加VPN连接
2. 配置参数:
   - **VPN提供商**: Windows (内置)
   - **连接名称**: Google-VPN
   - **服务器**: {public_ip}
   - **VPN类型**: IKEv2
   - **登录信息类型**: 用户名和密码
   - **用户名**: vpnuser
   - **密码**: {psk}

### 方法2: L2TP/IPSec (备用)
1. 设置 → 网络和Internet → VPN → 添加VPN连接
2. 配置参数:
   - **VPN提供商**: Windows (内置)
   - **连接名称**: Google-VPN-L2TP
   - **服务器**: {public_ip}
   - **VPN类型**: L2TP/IPSec (预共享密钥)
   - **预共享密钥**: {psk}
   - **用户名**: vpnuser
   - **密码**: {psk}

## 📱 移动设备连接

### iOS
1. 设置 → 通用 → VPN → 添加VPN配置
2. **类型**: IKEv2
3. **服务器**: {public_ip}
4. **远程ID**: {public_ip}
5. **用户名**: vpnuser
6. **密码**: {psk}

### Android
1. 设置 → 网络和互联网 → VPN → 添加VPN
2. **类型**: IKEv2/IPSec PSK
3. **服务器地址**: {public_ip}
4. **IPSec标识符**: {public_ip}
5. **预共享密钥**: {psk}

## ✅ 连接验证
1. 连接成功后访问: https://whatismyipaddress.com
2. 应显示IP: **{public_ip}** (美国弗吉尼亚州)
3. 测试Google服务: https://aistudio.google.com

## 🔧 故障排除
- 如果连接失败，尝试L2TP/IPSec协议
- 确保防火墙允许VPN流量
- 检查用户名密码是否正确

---
*生成时间: {subprocess.run(['date'], capture_output=True, text=True, shell=True).stdout.strip()}*
"""
        
        with open('VPN客户端配置指南.md', 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print("✅ 配置指南已生成: VPN客户端配置指南.md")
        print(f"\n🔑 快速连接信息:")
        print(f"服务器: {public_ip}")
        print(f"用户名: vpnuser")
        print(f"密码: {psk}")
        
    except FileNotFoundError:
        print("❌ 未找到部署信息文件")
    except Exception as e:
        print(f"❌ 生成配置指南失败: {e}")

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (1-7): ").strip()
            
            if choice == '1':
                run_script('vpn_deployment.py', '部署VPN服务器')
            elif choice == '2':
                run_script('check_vpn_status.py', '检查VPN服务状态')
            elif choice == '3':
                run_script('check_aws_resources.py', '检查AWS资源状态')
            elif choice == '4':
                generate_client_config()
            elif choice == '5':
                test_google_access()
            elif choice == '6':
                show_deployment_info()
            elif choice == '7':
                print("\n👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入1-7")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
