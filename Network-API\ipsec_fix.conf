config setup
    protostack=netkey
    dnssec-enable=no

conn %default
    left=%defaultroute
    right=%any

# 主要连接 - 支持现代客户端
conn Google-VPN
    also=%default
    authby=secret
    rightsourceip=***********-***********0
    rightdns=*******,*******
    auto=add
    # 支持更多算法组合
    ike=aes256-sha256-modp2048,aes256-sha1-modp2048,aes128-sha256-modp2048,aes128-sha1-modp2048,aes256-sha1-modp1024,aes128-sha1-modp1024,3des-sha1-modp1024
    esp=aes256-sha256,aes256-sha1,aes128-sha256,aes128-sha1,3des-sha1

# 兼容旧客户端
conn Google-VPN-Legacy
    also=%default
    authby=secret
    rightsourceip=***********1-************
    rightdns=*******,*******
    auto=add
    # 支持MODP1024和弱加密
    ike=aes256-sha1-modp1024,aes128-sha1-modp1024,3des-sha1-modp1024,aes256-sha1-modp1536,aes128-sha1-modp1536
    esp=aes256-sha1,aes128-sha1,3des-sha1

# EAP认证连接
conn Google-VPN-EAP
    also=%default
    leftauth=psk
    rightauth=eap-mschapv2
    rightsourceip=************-************
    rightdns=*******,*******
    auto=add
    eap_identity=%identity
    ike=aes256-sha256-modp2048,aes256-sha1-modp2048,aes128-sha256-modp2048,aes128-sha1-modp2048
    esp=aes256-sha256,aes256-sha1,aes128-sha256,aes128-sha1 