#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN客户端连接监控脚本
监控VPN连接状态并诊断连接问题
"""

import subprocess
import json
import os
import sys
import time
import socket
import boto3
from botocore.exceptions import ClientError
import threading
import datetime

def load_deployment_info():
    """加载部署信息"""
    try:
        with open('vpn_deployment_info.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 错误：未找到部署信息文件")
        return None

def check_server_status(deployment_info):
    """检查服务器状态"""
    print("=== 检查VPN服务器状态 ===")
    
    try:
        ec2 = boto3.client('ec2', region_name=deployment_info['region'])
        instance_id = deployment_info['instance_id']
        
        response = ec2.describe_instances(InstanceIds=[instance_id])
        if not response['Reservations']:
            print(f"❌ 实例 {instance_id} 不存在")
            return False
        
        instance = response['Reservations'][0]['Instances'][0]
        state = instance['State']['Name']
        public_ip = instance.get('PublicIpAddress', 'N/A')
        
        print(f"实例ID: {instance_id}")
        print(f"状态: {state}")
        print(f"公网IP: {public_ip}")
        
        if state == 'running':
            print("✅ 实例运行正常")
            return True
        else:
            print(f"⚠️  实例状态异常: {state}")
            return False
            
    except Exception as e:
        print(f"❌ 检查实例状态失败: {e}")
        return False

def check_vpn_services(public_ip):
    """检查VPN服务状态"""
    print(f"\n=== 检查VPN服务状态 ===")
    
    # 检查IPSec服务
    try:
        result = subprocess.run([
            'ssh', '-i', 'Google-VPN-Key.pem', '-o', 'StrictHostKeyChecking=no',
            f'ec2-user@{public_ip}', 'sudo systemctl status ipsec'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ IPSec服务运行正常")
        else:
            print("❌ IPSec服务异常")
            print(result.stderr)
    except Exception as e:
        print(f"❌ 检查IPSec服务失败: {e}")
    
    # 检查端口监听
    ports_to_check = [
        (500, 'UDP', 'IPSec IKE'),
        (4500, 'UDP', 'IPSec NAT-T'),
        (1701, 'UDP', 'L2TP')
    ]
    
    for port, protocol, description in ports_to_check:
        try:
            if protocol == 'UDP':
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            else:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            
            sock.settimeout(5)
            result = sock.connect_ex((public_ip, port))
            sock.close()
            
            if result == 0:
                print(f"✅ {description} 端口 ({protocol}/{port}) 可达")
            else:
                print(f"❌ {description} 端口 ({protocol}/{port}) 不可达")
        except Exception as e:
            print(f"❌ 检查 {description} 端口失败: {e}")

def monitor_connection_attempts(public_ip):
    """监控连接尝试"""
    print(f"\n=== 监控VPN连接尝试 ===")
    
    try:
        # 获取最近的IPSec日志
        result = subprocess.run([
            'ssh', '-i', 'Google-VPN-Key.pem', '-o', 'StrictHostKeyChecking=no',
            f'ec2-user@{public_ip}', 'sudo journalctl -u ipsec --since "10 minutes ago" --no-pager'
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            logs = result.stdout
            print("📋 最近10分钟的IPSec日志:")
            print("=" * 50)
            
            # 分析连接尝试
            connection_attempts = []
            failed_connections = []
            algorithm_mismatches = []
            auth_failures = []
            
            for line in logs.split('\n'):
                if 'packet from' in line and 'initial' in line:
                    connection_attempts.append(line.strip())
                elif 'no local proposal matches' in line:
                    failed_connections.append(line.strip())
                    algorithm_mismatches.append(line.strip())
                elif 'authentication failed' in line:
                    failed_connections.append(line.strip())
                    auth_failures.append(line.strip())
                elif 'connection has been authorized' in line:
                    print(f"✅ 成功连接: {line.strip()}")
            
            if connection_attempts:
                print(f"\n📊 连接尝试统计:")
                print(f"总连接尝试: {len(connection_attempts)}")
                print(f"失败连接: {len(failed_connections)}")
                print(f"算法不匹配: {len(algorithm_mismatches)}")
                print(f"认证失败: {len(auth_failures)}")
                
                if algorithm_mismatches:
                    print(f"\n🔍 算法不匹配问题分析:")
                    for failure in algorithm_mismatches[-3:]:
                        print(f"  - {failure}")
                        # 分析具体的算法问题
                        if 'MODP1024' in failure:
                            print("    💡 问题: 客户端使用MODP1024，服务器不支持")
                            print("    🔧 解决方案: 修改客户端配置或服务器配置")
                        elif '3DES' in failure:
                            print("    💡 问题: 客户端使用3DES加密，可能被阻止")
                            print("    🔧 解决方案: 使用AES加密")
                
                if auth_failures:
                    print(f"\n🔍 认证失败问题分析:")
                    for failure in auth_failures[-3:]:
                        print(f"  - {failure}")
                        print("    💡 问题: 预共享密钥或用户名密码错误")
                        print("    🔧 解决方案: 检查客户端配置的认证信息")
                
                if failed_connections and not algorithm_mismatches and not auth_failures:
                    print(f"\n❌ 其他失败连接:")
                    for failure in failed_connections[-3:]:
                        print(f"  - {failure}")
            else:
                print("📊 最近10分钟内无连接尝试")
                
        else:
            print("❌ 无法获取IPSec日志")
            
    except Exception as e:
        print(f"❌ 监控连接尝试失败: {e}")

def check_ipsec_config(public_ip):
    """检查IPSec配置"""
    print(f"\n=== 检查IPSec配置 ===")
    
    try:
        # 检查IPSec配置
        result = subprocess.run([
            'ssh', '-i', 'Google-VPN-Key.pem', '-o', 'StrictHostKeyChecking=no',
            f'ec2-user@{public_ip}', 'sudo cat /etc/ipsec.conf'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            config = result.stdout
            print("📋 当前IPSec配置:")
            print("=" * 30)
            print(config)
            
            # 检查支持的算法
            if 'modp1024' in config:
                print("⚠️  警告: 配置包含MODP1024，可能不被支持")
            if 'modp1536' in config:
                print("✅ 配置包含MODP1536，兼容性较好")
            if 'modp2048' in config:
                print("✅ 配置包含MODP2048，安全性较高")
                
        else:
            print("❌ 无法获取IPSec配置")
            
    except Exception as e:
        print(f"❌ 检查IPSec配置失败: {e}")

def check_firewall_rules(public_ip):
    """检查防火墙规则"""
    print(f"\n=== 检查防火墙规则 ===")
    
    try:
        # 检查iptables规则
        result = subprocess.run([
            'ssh', '-i', 'Google-VPN-Key.pem', '-o', 'StrictHostKeyChecking=no',
            f'ec2-user@{public_ip}', 'sudo iptables -L -n 2>/dev/null || echo "iptables not available"'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            rules = result.stdout
            if 'iptables not available' in rules:
                print("ℹ️  系统未使用iptables防火墙")
            else:
                print("📋 当前防火墙规则:")
                print("=" * 30)
                print(rules)
        else:
            print("❌ 无法获取防火墙规则")
            
    except Exception as e:
        print(f"❌ 检查防火墙规则失败: {e}")

def check_network_routing(public_ip):
    """检查网络路由"""
    print(f"\n=== 检查网络路由 ===")
    
    try:
        # 检查路由表
        result = subprocess.run([
            'ssh', '-i', 'Google-VPN-Key.pem', '-o', 'StrictHostKeyChecking=no',
            f'ec2-user@{public_ip}', 'ip route show'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            routes = result.stdout
            print("📋 当前路由表:")
            print("=" * 30)
            print(routes)
            
            # 检查IP转发
            result2 = subprocess.run([
                'ssh', '-i', 'Google-VPN-Key.pem', '-o', 'StrictHostKeyChecking=no',
                f'ec2-user@{public_ip}', 'cat /proc/sys/net/ipv4/ip_forward'
            ], capture_output=True, text=True, timeout=10)
            
            if result2.returncode == 0:
                ip_forward = result2.stdout.strip()
                if ip_forward == '1':
                    print("✅ IP转发已启用")
                else:
                    print("❌ IP转发未启用")
            else:
                print("❌ 无法检查IP转发状态")
                
        else:
            print("❌ 无法获取路由表")
            
    except Exception as e:
        print(f"❌ 检查网络路由失败: {e}")

def analyze_connection_problems(public_ip):
    """分析连接问题的根本原因"""
    print(f"\n=== 连接问题根本原因分析 ===")
    
    try:
        # 获取最近的失败连接日志
        result = subprocess.run([
            'ssh', '-i', 'Google-VPN-Key.pem', '-o', 'StrictHostKeyChecking=no',
            f'ec2-user@{public_ip}', 'sudo journalctl -u ipsec --since "30 minutes ago" --no-pager | grep -E "(no local proposal|authentication failed|packet from)"'
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0 and result.stdout.strip():
            logs = result.stdout
            print("🔍 分析最近30分钟的连接问题...")
            
            # 统计问题类型
            modp1024_count = 0
            auth_failures = 0
            other_failures = 0
            
            for line in logs.split('\n'):
                if 'MODP1024' in line or 'modp1024' in line:
                    modp1024_count += 1
                elif 'authentication failed' in line:
                    auth_failures += 1
                elif 'packet from' in line:
                    other_failures += 1
            
            print(f"\n📊 问题统计:")
            print(f"MODP1024算法问题: {modp1024_count}")
            print(f"认证失败: {auth_failures}")
            print(f"其他连接尝试: {other_failures}")
            
            # 提供解决方案
            print(f"\n🔧 解决方案建议:")
            
            if modp1024_count > 0:
                print("1. MODP1024算法问题:")
                print("   - 客户端使用MODP1024，但服务器不支持")
                print("   - 解决方案: 修改客户端配置使用MODP1536或MODP2048")
                print("   - 或者修改服务器配置支持MODP1024（不推荐）")
            
            if auth_failures > 0:
                print("2. 认证失败问题:")
                print("   - 检查客户端预共享密钥是否正确: GoogleVPN2023!")
                print("   - 检查用户名密码是否正确: vpnuser / GoogleVPN2023!")
                print("   - 确认客户端配置的认证方式")
            
            if other_failures > 0:
                print("3. 其他连接问题:")
                print("   - 检查网络环境是否阻止VPN流量")
                print("   - 检查防火墙设置")
                print("   - 尝试使用不同的VPN协议")
            
            # 提供具体的修复建议
            print(f"\n💡 立即修复建议:")
            print("1. 检查客户端配置:")
            print("   - 服务器地址: *************")
            print("   - 预共享密钥: GoogleVPN2023!")
            print("   - 用户名: vpnuser")
            print("   - 密码: GoogleVPN2023!")
            
            print("2. 尝试不同的连接方式:")
            print("   - IKEv2 + 用户名密码认证")
            print("   - IKEv2 + 预共享密钥认证")
            print("   - L2TP/IPSec")
            
            print("3. 如果问题持续:")
            print("   - 联系网络管理员检查网络限制")
            print("   - 尝试使用移动热点测试")
            print("   - 检查客户端软件版本")
            
        else:
            print("📊 最近30分钟内无连接问题记录")
            
    except Exception as e:
        print(f"❌ 分析连接问题失败: {e}")

def generate_diagnostic_report(deployment_info):
    """生成诊断报告"""
    print(f"\n=== VPN连接诊断报告 ===")
    print(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"服务器IP: {deployment_info['public_ip']}")
    
    # 检查服务器状态
    server_ok = check_server_status(deployment_info)
    
    if server_ok:
        # 检查VPN服务
        check_vpn_services(deployment_info['public_ip'])
        
        # 监控连接尝试
        monitor_connection_attempts(deployment_info['public_ip'])
        
        # 分析连接问题的根本原因
        analyze_connection_problems(deployment_info['public_ip'])
        
        # 检查配置
        check_ipsec_config(deployment_info['public_ip'])
        
        # 检查防火墙
        check_firewall_rules(deployment_info['public_ip'])
        
        # 检查路由
        check_network_routing(deployment_info['public_ip'])
        
        print(f"\n=== 诊断完成 ===")
        print("请根据上述信息分析VPN连接问题")
        print("💡 建议运行 'python test_client_connection.py monitor' 进行实时监控")
    else:
        print("❌ 服务器状态异常，无法进行进一步诊断")

def monitor_mode(deployment_info):
    """监控模式 - 持续监控连接状态"""
    print("🔍 进入监控模式，按Ctrl+C退出")
    print("监控VPN连接状态...")
    print("💡 实时分析连接问题并提供解决方案")
    
    try:
        while True:
            print(f"\n[{datetime.datetime.now().strftime('%H:%M:%S')}] 检查连接状态...")
            
            # 检查最近的连接尝试
            try:
                result = subprocess.run([
                    'ssh', '-i', 'Google-VPN-Key.pem', '-o', 'StrictHostKeyChecking=no',
                    f'ec2-user@{deployment_info["public_ip"]}', 
                    'sudo journalctl -u ipsec --since "2 minutes ago" --no-pager | grep -E "(packet from|authentication failed|no local proposal|connection has been authorized)"'
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0 and result.stdout.strip():
                    print("📊 最近2分钟的活动:")
                    activity_found = False
                    
                    for line in result.stdout.strip().split('\n'):
                        if line.strip():
                            activity_found = True
                            if 'connection has been authorized' in line:
                                print(f"  ✅ 成功连接: {line.strip()}")
                            elif 'no local proposal matches' in line:
                                print(f"  ❌ 算法不匹配: {line.strip()}")
                                if 'MODP1024' in line:
                                    print("    💡 问题: 客户端使用MODP1024，服务器不支持")
                                    print("    🔧 解决方案: 修改客户端配置使用MODP1536或MODP2048")
                            elif 'authentication failed' in line:
                                print(f"  ❌ 认证失败: {line.strip()}")
                                print("    💡 问题: 预共享密钥或用户名密码错误")
                                print("    🔧 解决方案: 检查认证信息 - 密钥: GoogleVPN2023!")
                            elif 'packet from' in line:
                                print(f"  📡 连接尝试: {line.strip()}")
                            else:
                                print(f"  📋 其他活动: {line.strip()}")
                    
                    if not activity_found:
                        print("📊 最近2分钟内无活动")
                else:
                    print("📊 最近2分钟内无活动")
                    
            except Exception as e:
                print(f"❌ 监控检查失败: {e}")
            
            # 每10秒检查一次，提高监控频率
            print("⏳ 等待10秒后继续监控...")
            time.sleep(0)
            
    except KeyboardInterrupt:
        print("\n🛑 监控模式已退出")
        print("📋 监控总结:")
        print("  - 使用 'python test_client_connection.py' 进行完整诊断")
        print("  - 根据上述分析结果修复客户端配置")
        print("  - 如果问题持续，请联系技术支持")

def main():
    print("VPN客户端连接监控工具")
    print("=====================")
    
    # 加载部署信息
    deployment_info = load_deployment_info()
    if not deployment_info:
        print("❌ 无法加载部署信息，请确保vpn_deployment_info.json文件存在")
        return
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == 'monitor':
        monitor_mode(deployment_info)
    else:
        generate_diagnostic_report(deployment_info)

if __name__ == "__main__":
    main()
