<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN管理扩展示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            width: 300px;
            padding: 20px;
            margin: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
        }
        .status.online {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.offline {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .config-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 12px;
        }
        .config-info strong {
            color: #495057;
        }
        .buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        button {
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        button:hover {
            opacity: 0.8;
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 15px;
            max-height: 150px;
            overflow-y: auto;
            font-size: 11px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>🚀 VPN管理器</h3>
    </div>

    <div id="status" class="status loading">
        正在检查VPN状态...
    </div>

    <div id="config-info" class="config-info" style="display: none;">
        <strong>服务器信息:</strong><br>
        IP: <span id="server-ip">-</span><br>
        协议: <span id="protocols">-</span><br>
        状态: <span id="instance-state">-</span>
    </div>

    <div class="buttons">
        <button id="refresh-btn" class="btn-primary" onclick="checkStatus()">
            🔄 刷新状态
        </button>
        <button id="test-btn" class="btn-success" onclick="testConnection()">
            🧪 测试连接
        </button>
        <button id="config-btn" class="btn-warning" onclick="showConfig()">
            ⚙️ 显示配置
        </button>
        <button id="deploy-btn" class="btn-secondary" onclick="deployVPN()">
            🚀 重新部署
        </button>
    </div>

    <div id="log" class="log" style="display: none;"></div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            logDiv.style.display = 'block';
        }

        function setStatus(type, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function setButtonsEnabled(enabled) {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(btn => btn.disabled = !enabled);
        }

        async function checkStatus() {
            setStatus('loading', '正在检查VPN状态...');
            setButtonsEnabled(false);
            
            try {
                const response = await fetch(`${API_BASE}/status`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    const vpnData = data.data;
                    setStatus('online', '✅ VPN服务器在线');
                    
                    // 显示配置信息
                    document.getElementById('server-ip').textContent = vpnData.server_ip;
                    document.getElementById('instance-state').textContent = vpnData.instance_state;
                    
                    const protocols = [];
                    if (vpnData.protocols.ikev2) protocols.push('IKEv2');
                    if (vpnData.protocols.l2tp) protocols.push('L2TP');
                    document.getElementById('protocols').textContent = protocols.join(', ') || '无';
                    
                    document.getElementById('config-info').style.display = 'block';
                    log('VPN状态检查成功');
                } else {
                    setStatus('offline', '❌ VPN服务器离线');
                    document.getElementById('config-info').style.display = 'none';
                    log(`状态检查失败: ${data.message}`);
                }
            } catch (error) {
                setStatus('offline', '❌ 无法连接到API');
                document.getElementById('config-info').style.display = 'none';
                log(`连接错误: ${error.message}`);
            }
            
            setButtonsEnabled(true);
        }

        async function testConnection() {
            setButtonsEnabled(false);
            log('开始测试VPN连接...');
            
            try {
                const response = await fetch(`${API_BASE}/test`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ type: 'basic' })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    const results = data.data.results;
                    log(`连接测试结果:`);
                    log(`  IKEv2端口: ${results.ikev2_port ? '✅' : '❌'}`);
                    log(`  NAT-T端口: ${results.nat_t_port ? '✅' : '❌'}`);
                    log(`  L2TP端口: ${results.l2tp_port ? '✅' : '❌'}`);
                    log(`  总体状态: ${data.data.overall ? '✅ 正常' : '❌ 异常'}`);
                } else {
                    log(`测试失败: ${data.message}`);
                }
            } catch (error) {
                log(`测试错误: ${error.message}`);
            }
            
            setButtonsEnabled(true);
        }

        async function showConfig() {
            setButtonsEnabled(false);
            log('获取VPN配置信息...');
            
            try {
                const response = await fetch(`${API_BASE}/config`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    const config = data.data;
                    log('VPN配置信息:');
                    log(`  服务器: ${config.server_ip}`);
                    log(`  预共享密钥: ${config.psk}`);
                    log(`  用户账号: ${Object.keys(config.users).join(', ')}`);
                    log(`  DNS服务器: ${config.dns.join(', ')}`);
                } else {
                    log(`获取配置失败: ${data.message}`);
                }
            } catch (error) {
                log(`配置错误: ${error.message}`);
            }
            
            setButtonsEnabled(true);
        }

        async function deployVPN() {
            if (!confirm('确定要重新部署VPN服务器吗？这可能需要几分钟时间。')) {
                return;
            }
            
            setButtonsEnabled(false);
            setStatus('loading', '正在部署VPN服务器...');
            log('开始部署VPN服务器...');
            
            try {
                const response = await fetch(`${API_BASE}/deploy`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    setStatus('online', '✅ 部署成功');
                    log('VPN服务器部署成功');
                    setTimeout(checkStatus, 2000); // 2秒后检查状态
                } else {
                    setStatus('offline', '❌ 部署失败');
                    log(`部署失败: ${data.message}`);
                }
            } catch (error) {
                setStatus('offline', '❌ 部署错误');
                log(`部署错误: ${error.message}`);
            }
            
            setButtonsEnabled(true);
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html>
