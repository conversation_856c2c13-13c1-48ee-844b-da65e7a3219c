#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN API接口 - 为浏览器扩展提供REST API
支持VPN状态查询、连接管理等功能
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import json
import os
import subprocess
import boto3
from datetime import datetime

app = Flask(__name__)
CORS(app)  # 允许跨域请求

def load_deployment_info():
    """加载部署信息"""
    try:
        with open('vpn_deployment_info.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return None

def load_vpn_config():
    """加载VPN配置"""
    try:
        import configparser
        config = configparser.ConfigParser()
        config.read('vpn_config.ini')
        return config
    except:
        return None

@app.route('/api/status', methods=['GET'])
def get_vpn_status():
    """获取VPN服务器状态"""
    deployment_info = load_deployment_info()
    if not deployment_info:
        return jsonify({
            'status': 'error',
            'message': 'VPN服务器未部署'
        }), 404
    
    try:
        # 检查AWS实例状态
        ec2 = boto3.client('ec2', region_name=deployment_info['region'])
        response = ec2.describe_instances(InstanceIds=[deployment_info['instance_id']])
        instance = response['Reservations'][0]['Instances'][0]
        
        # 检查端口连通性
        import socket
        def test_port(ip, port):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.settimeout(3)
                result = sock.connect_ex((ip, port))
                sock.close()
                return result == 0
            except:
                return False
        
        ports_status = {
            'ikev2': test_port(deployment_info['public_ip'], 500),
            'l2tp': test_port(deployment_info['public_ip'], 1701),
            'nat_t': test_port(deployment_info['public_ip'], 4500)
        }
        
        return jsonify({
            'status': 'success',
            'data': {
                'server_ip': deployment_info['public_ip'],
                'instance_state': instance['State']['Name'],
                'deployment_time': deployment_info.get('deployment_time'),
                'ports': ports_status,
                'protocols': {
                    'ikev2': ports_status['ikev2'] and ports_status['nat_t'],
                    'l2tp': ports_status['l2tp']
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'检查状态失败: {str(e)}'
        }), 500

@app.route('/api/config', methods=['GET'])
def get_vpn_config():
    """获取VPN配置信息"""
    deployment_info = load_deployment_info()
    vpn_config = load_vpn_config()
    
    if not deployment_info:
        return jsonify({
            'status': 'error',
            'message': 'VPN服务器未部署'
        }), 404
    
    config_data = {
        'server_ip': deployment_info['public_ip'],
        'psk': 'GoogleVPN2023!',
        'users': {
            'vpnuser': 'GoogleVPN2023!',
            'testuser': 'GoogleVPN2023!',
            'admin': 'GoogleVPN2023!'
        },
        'protocols': {
            'ikev2': {
                'port': 500,
                'nat_port': 4500,
                'client_subnet': '**********/24'
            },
            'l2tp': {
                'port': 1701,
                'client_subnet': '**********/24'
            }
        },
        'dns': ['*******', '*******']
    }
    
    return jsonify({
        'status': 'success',
        'data': config_data
    })

@app.route('/api/test', methods=['POST'])
def test_vpn_connection():
    """测试VPN连接"""
    data = request.get_json()
    test_type = data.get('type', 'basic')
    
    deployment_info = load_deployment_info()
    if not deployment_info:
        return jsonify({
            'status': 'error',
            'message': 'VPN服务器未部署'
        }), 404
    
    try:
        if test_type == 'basic':
            # 基础端口测试
            import socket
            def test_port(ip, port):
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    sock.settimeout(5)
                    result = sock.connect_ex((ip, port))
                    sock.close()
                    return result == 0
                except:
                    return False
            
            results = {
                'ikev2_port': test_port(deployment_info['public_ip'], 500),
                'nat_t_port': test_port(deployment_info['public_ip'], 4500),
                'l2tp_port': test_port(deployment_info['public_ip'], 1701)
            }
            
            return jsonify({
                'status': 'success',
                'data': {
                    'test_type': 'basic',
                    'results': results,
                    'overall': all(results.values())
                }
            })
            
        elif test_type == 'google':
            # Google服务访问测试
            try:
                result = subprocess.run([
                    'python3', 'test_vpn_connections.py'
                ], capture_output=True, text=True, timeout=30)
                
                return jsonify({
                    'status': 'success',
                    'data': {
                        'test_type': 'google',
                        'output': result.stdout,
                        'success': result.returncode == 0
                    }
                })
            except subprocess.TimeoutExpired:
                return jsonify({
                    'status': 'error',
                    'message': 'Google服务测试超时'
                }), 408
                
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'测试失败: {str(e)}'
        }), 500

@app.route('/api/deploy', methods=['POST'])
def deploy_vpn():
    """部署VPN服务器"""
    try:
        result = subprocess.run([
            'python3', 'vpn_deployment.py'
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            return jsonify({
                'status': 'success',
                'message': 'VPN服务器部署成功',
                'output': result.stdout
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'VPN服务器部署失败',
                'error': result.stderr
            }), 500
            
    except subprocess.TimeoutExpired:
        return jsonify({
            'status': 'error',
            'message': '部署超时'
        }), 408
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'部署失败: {str(e)}'
        }), 500

@app.route('/api/info', methods=['GET'])
def get_system_info():
    """获取系统信息"""
    return jsonify({
        'status': 'success',
        'data': {
            'version': '1.0.0',
            'name': 'Network-API VPN Server',
            'description': '支持IKEv2和L2TP协议的VPN服务器',
            'supported_platforms': ['Windows', 'macOS', 'iOS', 'Android'],
            'api_version': 'v1',
            'timestamp': datetime.now().isoformat()
        }
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'status': 'error',
        'message': 'API端点不存在'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'status': 'error',
        'message': '服务器内部错误'
    }), 500

if __name__ == '__main__':
    print("🚀 启动VPN API服务器...")
    print("API端点:")
    print("  GET  /api/status  - 获取VPN状态")
    print("  GET  /api/config  - 获取VPN配置")
    print("  POST /api/test    - 测试VPN连接")
    print("  POST /api/deploy  - 部署VPN服务器")
    print("  GET  /api/info    - 获取系统信息")
    print("\n访问地址: http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
