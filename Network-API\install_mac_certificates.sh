#!/bin/bash
# Mac VPN证书安装脚本

echo "=== Mac VPN证书安装工具 ==="
echo "此脚本将帮助您在Mac上安装VPN证书"
echo ""

# 检查证书文件
if [ ! -f "ca.crt" ] || [ ! -f "mac_client.p12" ]; then
    echo "❌ 错误：证书文件不存在"
    echo "请确保以下文件在当前目录："
    echo "  - ca.crt (CA根证书)"
    echo "  - mac_client.p12 (客户端证书包)"
    exit 1
fi

echo "✅ 证书文件检查通过"
echo ""

# 安装CA证书到系统钥匙串
echo "1. 安装CA根证书到系统钥匙串..."
if security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain ca.crt; then
    echo "✅ CA证书已安装到系统钥匙串"
else
    echo "⚠️ CA证书安装失败，可能需要管理员权限"
    echo "请手动双击 ca.crt 文件进行安装"
fi

echo ""

# 安装客户端证书到登录钥匙串
echo "2. 安装客户端证书到登录钥匙串..."
if security import mac_client.p12 -k ~/Library/Keychains/login.keychain -P "GoogleVPN2023!" -T /System/Library/CoreServices/SystemUIServer.app -T /usr/bin/security; then
    echo "✅ 客户端证书已安装到登录钥匙串"
else
    echo "⚠️ 客户端证书安装失败"
    echo "请手动双击 mac_client.p12 文件进行安装"
    echo "密码: GoogleVPN2023!"
fi

echo ""

# 设置证书信任
echo "3. 设置证书信任..."
echo "请在钥匙串访问中找到 'Google-VPN-CA' 证书"
echo "双击证书 → 信任 → 设置为'始终信任'"

echo ""

# 显示VPN配置信息
echo "4. VPN连接配置信息："
echo "================================"
echo "服务器地址: *************"
echo "VPN类型: IKEv2"
echo "认证方式: 证书"
echo "用户认证: 无 (使用证书)"
echo "机器认证: 证书 (选择刚安装的客户端证书)"
echo ""

echo "5. 手动配置VPN连接："
echo "================================"
echo "a) 打开 系统偏好设置 → 网络"
echo "b) 点击左下角的 '+' 按钮"
echo "c) 接口: VPN"
echo "d) VPN类型: IKEv2"
echo "e) 服务名称: Google VPN"
echo "f) 配置: 默认"
echo "g) 服务器地址: *************"
echo "h) 远程ID: *************"
echo "i) 本地ID: (留空)"
echo ""

echo "6. 认证设置："
echo "================================"
echo "a) 点击 '认证设置...'"
echo "b) 认证方法: 证书"
echo "c) 证书: 选择 'Google VPN Client' 证书"
echo "d) 点击 '好'"
echo ""

echo "7. 连接测试："
echo "================================"
echo "a) 点击 '连接'"
echo "b) 连接成功后访问 https://whatismyipaddress.com"
echo "c) 确认IP地址显示为: *************"
echo ""

echo "🔧 故障排除："
echo "================================"
echo "如果连接失败，请尝试："
echo "1. 检查证书是否正确安装"
echo "2. 确认CA证书已设置为'始终信任'"
echo "3. 重启网络服务: sudo dscacheutil -flushcache"
echo "4. 使用备用PSK连接方式"
echo ""

echo "📋 备用PSK连接方式："
echo "================================"
echo "如果证书连接失败，可使用预共享密钥："
echo "- 认证方法: 共享密钥"
echo "- 共享密钥: GoogleVPN2023!"
echo "- 用户名: vpnuser"
echo "- 密码: GoogleVPN2023!"
echo ""

echo "✅ 证书安装完成！"
echo "请按照上述步骤配置VPN连接。"
