# SSH连接问题手动修复指南

## 问题确认
SSH连接失败的根本原因：服务器端 `authorized_keys` 文件配置不正确

## 解决方案

### 方法1：通过AWS控制台连接修复

1. **登录AWS控制台**
   - 进入EC2服务
   - 找到实例 `i-0833cd3de301f16f4`
   - 点击"连接" -> "EC2 Instance Connect"

2. **执行修复命令**
```bash
# 创建.ssh目录
sudo mkdir -p /home/<USER>/.ssh
sudo chmod 700 /home/<USER>/.ssh

# 从实例元数据获取公钥
curl -s http://169.254.169.254/latest/meta-data/public-keys/0/openssh-key | sudo tee /home/<USER>/.ssh/authorized_keys

# 设置正确权限
sudo chmod 600 /home/<USER>/.ssh/authorized_keys
sudo chown -R ec2-user:ec2-user /home/<USER>/.ssh

# 重启SSH服务
sudo systemctl restart sshd
```

### 方法2：重新部署实例

如果方法1不可行，建议重新部署一个新实例：

```python
# 运行以下Python脚本
python3 vpn_deployment.py
```

### 方法3：使用Session Manager

如果实例配置了IAM角色，可以通过Session Manager连接：

1. 在AWS控制台中选择实例
2. 点击"连接" -> "Session Manager"
3. 执行上述修复命令

## 验证SSH连接

修复后，使用以下命令测试：

```bash
ssh -i Google-VPN-Key.pem -o StrictHostKeyChecking=no ec2-user@*************
```

## VPN服务器信息

- **实例ID**: `i-0b8c401f5950248c`
- **公网IP**: `*************`
- **密钥文件**: `Google-VPN-Key.pem`
- **用户名**: `ec2-user`

## 预期结果

SSH连接成功后，VPN客户端将能够：
- 正常通过SSH端口(22)访问服务器
- 使用VPN连接时无SSH限制
- 享受智能防护（防止暴力破解攻击）

## 故障排除

如果SSH仍然失败：
1. 检查安全组是否允许SSH端口22
2. 确认密钥文件权限为400或600
3. 验证使用正确的用户名（ec2-user）
4. 检查实例是否在运行状态
