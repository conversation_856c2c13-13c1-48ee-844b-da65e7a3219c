#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN连接方式测试脚本
测试3种VPN连接方式的可用性
"""

import subprocess
import json
import os
import sys
import time
import socket
import boto3
from botocore.exceptions import ClientError

def load_deployment_info():
    """加载部署信息"""
    try:
        with open('vpn_deployment_info.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 错误：未找到部署信息文件")
        return None

def check_security_group(deployment_info):
    """检查安全组配置"""
    print("=== 检查安全组配置 ===")
    
    try:
        ec2 = boto3.client('ec2', region_name=deployment_info['region'])
        sg_id = deployment_info['security_group_id']
        
        response = ec2.describe_security_groups(GroupIds=[sg_id])
        if not response['SecurityGroups']:
            print(f"❌ 安全组 {sg_id} 不存在")
            return False
        
        sg = response['SecurityGroups'][0]
        print(f"安全组ID: {sg_id}")
        print(f"安全组名称: {sg['GroupName']}")
        
        print("\n入站规则:")
        for rule in sg['IpPermissions']:
            protocol = rule['IpProtocol']
            if protocol == '-1':
                print("  - 所有流量")
            elif 'FromPort' in rule:
                port_range = f"{rule['FromPort']}"
                if rule['FromPort'] != rule['ToPort']:
                    port_range += f"-{rule['ToPort']}"
                print(f"  - {protocol.upper()}/{port_range}")
            else:
                print(f"  - {protocol.upper()}")
        
        # 检查必要端口
        required_ports = [
            (22, 'tcp', 'SSH'),
            (500, 'udp', 'IPSec IKE'),
            (4500, 'udp', 'IPSec NAT-T'),
            (1701, 'udp', 'L2TP')
        ]
        
        print("\n端口检查:")
        for port, protocol, description in required_ports:
            found = False
            for rule in sg['IpPermissions']:
                if rule['IpProtocol'] == protocol:
                    if 'FromPort' in rule and rule['FromPort'] <= port <= rule['ToPort']:
                        found = True
                        break
                elif rule['IpProtocol'] == '-1':  # 所有协议
                    found = True
                    break
            
            status = "✅" if found else "❌"
            print(f"  {status} {description} ({protocol.upper()}/{port})")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查安全组失败: {e}")
        return False

def fix_security_group(deployment_info):
    """修复安全组配置"""
    print("\n=== 修复安全组配置 ===")
    
    try:
        ec2 = boto3.client('ec2', region_name=deployment_info['region'])
        sg_id = deployment_info['security_group_id']
        
        # 添加SSH端口规则
        try:
            ec2.authorize_security_group_ingress(
                GroupId=sg_id,
                IpPermissions=[
                    {
                        'IpProtocol': 'tcp',
                        'FromPort': 22,
                        'ToPort': 22,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    }
                ]
            )
            print("✅ 添加SSH端口规则成功")
        except ClientError as e:
            if 'InvalidPermission.Duplicate' in str(e):
                print("✅ SSH端口规则已存在")
            else:
                print(f"❌ 添加SSH端口规则失败: {e}")
        
        # 添加L2TP端口规则
        try:
            ec2.authorize_security_group_ingress(
                GroupId=sg_id,
                IpPermissions=[
                    {
                        'IpProtocol': 'udp',
                        'FromPort': 1701,
                        'ToPort': 1701,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    }
                ]
            )
            print("✅ 添加L2TP端口规则成功")
        except ClientError as e:
            if 'InvalidPermission.Duplicate' in str(e):
                print("✅ L2TP端口规则已存在")
            else:
                print(f"❌ 添加L2TP端口规则失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复安全组失败: {e}")
        return False

def test_port_connectivity(host, port, protocol='tcp', timeout=5):
    """测试端口连通性"""
    try:
        if protocol.lower() == 'tcp':
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        else:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        
        return result == 0
    except Exception:
        return False

def test_vpn_ports(public_ip):
    """测试VPN相关端口"""
    print(f"\n=== 测试VPN端口连通性 ===")
    
    ports_to_test = [
        (22, 'tcp', 'SSH'),
        (500, 'udp', 'IPSec IKE'),
        (4500, 'udp', 'IPSec NAT-T'),
        (1701, 'udp', 'L2TP')
    ]
    
    results = {}
    for port, protocol, description in ports_to_test:
        print(f"测试 {description} ({protocol.upper()}/{port})...")
        is_open = test_port_connectivity(public_ip, port, protocol)
        status = "✅ 可达" if is_open else "❌ 不可达"
        print(f"  {status}")
        results[f"{protocol}_{port}"] = is_open
    
    return results

def generate_connection_configs(public_ip):
    """生成3种连接方式的配置"""
    print(f"\n=== VPN连接配置 ===")
    
    configs = {
        "ikev2_eap": {
            "name": "IKEv2 + EAP认证（用户名密码）",
            "type": "IKEv2",
            "server": public_ip,
            "auth_method": "用户名密码",
            "username": "vpnuser",
            "password": "GoogleVPN2023!",
            "description": "推荐方式，支持Windows/iOS/Android"
        },
        "ikev2_psk": {
            "name": "IKEv2 + PSK认证（预共享密钥）",
            "type": "IKEv2",
            "server": public_ip,
            "auth_method": "预共享密钥",
            "psk": "GoogleVPN2023!",
            "description": "备用方式，兼容性好"
        },
        "l2tp_ipsec": {
            "name": "L2TP/IPSec",
            "type": "L2TP/IPSec",
            "server": public_ip,
            "auth_method": "预共享密钥 + 用户名密码",
            "psk": "GoogleVPN2023!",
            "username": "vpnuser",
            "password": "GoogleVPN2023!",
            "description": "兼容性支持，适用于老设备"
        }
    }
    
    for key, config in configs.items():
        print(f"\n📱 {config['name']}")
        print(f"   类型: {config['type']}")
        print(f"   服务器: {config['server']}")
        print(f"   认证: {config['auth_method']}")
        if 'username' in config:
            print(f"   用户名: {config['username']}")
        if 'password' in config:
            print(f"   密码: {config['password']}")
        if 'psk' in config:
            print(f"   预共享密钥: {config['psk']}")
        print(f"   说明: {config['description']}")
    
    return configs

def create_test_instructions():
    """创建测试说明"""
    print(f"\n=== 连接测试说明 ===")
    
    instructions = """
🔧 手动测试步骤:

1. IKEv2 + EAP认证测试:
   - Windows: 设置 → 网络和Internet → VPN → 添加VPN连接
   - 选择IKEv2，使用用户名密码认证
   - 连接后访问 https://whatismyipaddress.com 确认IP

2. IKEv2 + PSK认证测试:
   - 某些客户端支持PSK认证
   - 配置预共享密钥进行连接

3. L2TP/IPSec测试:
   - Windows: 选择L2TP/IPSec (预共享密钥)
   - 配置预共享密钥和用户名密码

✅ 连接验证:
   - 连接成功后IP应显示: *************
   - 测试Google服务访问: https://www.google.com
   - 测试YouTube访问: https://www.youtube.com

⚠️ 注意事项:
   - 如果SSH端口不可达，需要先修复安全组
   - 确保防火墙允许VPN流量
   - 某些网络环境可能阻止VPN连接
"""
    
    print(instructions)

def main():
    """主函数"""
    print("VPN连接方式测试工具")
    print("====================")
    
    # 加载部署信息
    deployment_info = load_deployment_info()
    if not deployment_info:
        return
    
    public_ip = deployment_info['public_ip']
    print(f"VPN服务器: {public_ip}")
    
    # 1. 检查安全组配置
    if not check_security_group(deployment_info):
        return
    
    # 2. 测试端口连通性
    port_results = test_vpn_ports(public_ip)
    
    # 3. 如果SSH不可达，尝试修复安全组
    if not port_results.get('tcp_22', False):
        print("\n⚠️ SSH端口不可达，尝试修复安全组...")
        fix_security_group(deployment_info)
        
        # 重新测试
        print("\n重新测试端口连通性...")
        time.sleep(5)  # 等待规则生效
        port_results = test_vpn_ports(public_ip)
    
    # 4. 生成连接配置
    configs = generate_connection_configs(public_ip)
    
    # 5. 创建测试说明
    create_test_instructions()
    
    # 6. 总结
    print(f"\n{'='*50}")
    print("测试总结")
    print(f"{'='*50}")
    
    vpn_ports_ok = (
        port_results.get('udp_500', False) and 
        port_results.get('udp_4500', False)
    )
    
    if vpn_ports_ok:
        print("✅ VPN核心端口可达，支持IKEv2连接")
    else:
        print("❌ VPN核心端口不可达，需要检查网络配置")
    
    if port_results.get('udp_1701', False):
        print("✅ L2TP端口可达，支持L2TP/IPSec连接")
    else:
        print("⚠️ L2TP端口不可达，L2TP/IPSec可能无法使用")
    
    if port_results.get('tcp_22', False):
        print("✅ SSH端口可达，可以进行服务器管理")
    else:
        print("⚠️ SSH端口不可达，无法直接管理服务器")
    
    print(f"\n📋 建议测试顺序:")
    print(f"1. 优先测试: IKEv2 + EAP认证（用户名密码）")
    print(f"2. 备用测试: IKEv2 + PSK认证（预共享密钥）")
    print(f"3. 兼容测试: L2TP/IPSec")

if __name__ == "__main__":
    main()
