import boto3
import json
from botocore.exceptions import ClientError
import logging
import configparser
import os
import sys
import time
from datetime import datetime

class GoogleVPNDeployment:
    def __init__(self, region='us-east-1'):
        self.region = region
        try:
            self.ec2 = boto3.client('ec2', region_name=region)
            self.iam = boto3.client('iam', region_name=region)
            self.cloudformation = boto3.client('cloudformation', region_name=region)
        except Exception as e:
            print(f"错误：无法初始化AWS客户端: {e}")
            sys.exit(1)
        
        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.config = configparser.ConfigParser()
        config_file = 'vpn_config.ini'
        if os.path.exists(config_file):
            self.config.read(config_file)
            self.logger.info(f"配置文件 {config_file} 已加载")
        else:
            self.logger.warning(f"配置文件 {config_file} 不存在，将使用默认配置")
            self.config['VPN'] = {'PreSharedKey': 'GoogleVPN2023!'}
        
    def get_existing_vpc_resources(self):
        """检查是否存在已有的VPC资源可以复用"""
        try:
            # 先检查vpn_deployment_info.json文件中是否已有部署信息
            if os.path.exists('vpn_deployment_info.json'):
                with open('vpn_deployment_info.json', 'r') as f:
                    deployment_info = json.load(f)

                instance_id = deployment_info.get('instance_id')
                vpc_id = deployment_info.get('vpc_id')
                subnet_id = deployment_info.get('subnet_id')
                security_group_id = deployment_info.get('security_group_id')

                print(f"🔍 检查现有部署信息...")
                print(f"实例ID: {instance_id}")
                print(f"VPC ID: {vpc_id}")
                print(f"子网ID: {subnet_id}")
                print(f"安全组ID: {security_group_id}")

                # 验证这些资源是否仍然存在并且可用
                try:
                    # 检查实例是否存在并获取状态
                    instances = self.ec2.describe_instances(InstanceIds=[instance_id])
                    if not instances['Reservations']:
                        self.logger.info("记录的实例不存在，需要重新创建资源")
                        return None
                    
                    # 检查VPC是否存在
                    vpcs = self.ec2.describe_vpcs(VpcIds=[vpc_id])
                    if not vpcs['Vpcs']:
                        self.logger.info("记录的VPC不存在，需要重新创建资源")
                        return None
                    
                    # 检查子网是否存在
                    subnets = self.ec2.describe_subnets(SubnetIds=[subnet_id])
                    if not subnets['Subnets']:
                        self.logger.info("记录的子网不存在，需要重新创建资源")
                        return None
                    
                    # 检查安全组是否存在
                    security_groups = self.ec2.describe_security_groups(GroupIds=[security_group_id])
                    if not security_groups['SecurityGroups']:
                        self.logger.info("记录的安全组不存在，需要重新创建资源")
                        return None
                    
                    self.logger.info(f"找到并验证现有资源: VPC={vpc_id}, 子网={subnet_id}, 安全组={security_group_id}")
                    return {
                        'vpc_id': vpc_id,
                        'subnet_id': subnet_id,
                        'security_group_id': security_group_id
                    }
                except ClientError:
                    self.logger.info("验证现有资源时出错，可能需要重新创建")
                    return None
            
            # 如果没有部署信息文件，则查找标记为Google-VPN-VPC的VPC
            vpcs = self.ec2.describe_vpcs(Filters=[
                {'Name': 'tag:Name', 'Values': ['Google-VPN-VPC']}
            ])
            
            if vpcs['Vpcs']:
                vpc_id = vpcs['Vpcs'][0]['VpcId']
                self.logger.info(f"找到现有的VPC: {vpc_id}")
                
                # 查找关联的子网
                subnets = self.ec2.describe_subnets(Filters=[
                    {'Name': 'vpc-id', 'Values': [vpc_id]}
                ])
                
                if subnets['Subnets']:
                    subnet_id = subnets['Subnets'][0]['SubnetId']
                    self.logger.info(f"找到现有的子网: {subnet_id}")
                    
                    # 查找安全组
                    security_groups = self.ec2.describe_security_groups(Filters=[
                        {'Name': 'vpc-id', 'Values': [vpc_id]},
                        {'Name': 'group-name', 'Values': ['Google-VPN-SG']}
                    ])
                    
                    if security_groups['SecurityGroups']:
                        sg_id = security_groups['SecurityGroups'][0]['GroupId']
                        self.logger.info(f"找到现有的安全组: {sg_id}")
                        
                        return {
                            'vpc_id': vpc_id,
                            'subnet_id': subnet_id,
                            'security_group_id': sg_id
                        }
            return None
        except ClientError as e:
            self.logger.warning(f"检查现有VPC资源时出错: {e}")
            return None

    def create_vpc(self):
        """创建VPC用于VPN部署"""
        try:
            self.logger.info("正在创建VPC...")
            # 创建VPC
            vpc_response = self.ec2.create_vpc(
                CidrBlock='10.0.0.0/16',
                TagSpecifications=[
                    {
                        'ResourceType': 'vpc',
                        'Tags': [
                            {
                                'Key': 'Name',
                                'Value': 'Google-VPN-VPC'
                            }
                        ]
                    }
                ]
            )
            vpc_id = vpc_response['Vpc']['VpcId']
            self.logger.info(f"VPC创建成功: {vpc_id}")
            
            # 启用DNS支持
            self.ec2.modify_vpc_attribute(
                VpcId=vpc_id,
                EnableDnsSupport={'Value': True}
            )
            self.ec2.modify_vpc_attribute(
                VpcId=vpc_id,
                EnableDnsHostnames={'Value': True}
            )
            
            # 创建子网
            self.logger.info("正在创建子网...")
            subnet_response = self.ec2.create_subnet(
                VpcId=vpc_id,
                CidrBlock='********/24',
                AvailabilityZone=self.region + 'a'
            )
            subnet_id = subnet_response['Subnet']['SubnetId']
            self.logger.info(f"子网创建成功: {subnet_id}")
            
            # 创建互联网网关
            self.logger.info("正在创建互联网网关...")
            igw_response = self.ec2.create_internet_gateway()
            igw_id = igw_response['InternetGateway']['InternetGatewayId']
            self.logger.info(f"互联网网关创建成功: {igw_id}")
            
            # 将互联网网关附加到VPC
            self.ec2.attach_internet_gateway(
                InternetGatewayId=igw_id,
                VpcId=vpc_id
            )
            self.logger.info("互联网网关已附加到VPC")
            
            # 创建路由表
            self.logger.info("正在创建路由表...")
            route_table_response = self.ec2.create_route_table(VpcId=vpc_id)
            route_table_id = route_table_response['RouteTable']['RouteTableId']
            self.logger.info(f"路由表创建成功: {route_table_id}")
            
            # 添加默认路由到互联网网关
            self.ec2.create_route(
                RouteTableId=route_table_id,
                DestinationCidrBlock='0.0.0.0/0',
                GatewayId=igw_id
            )
            self.logger.info("默认路由已添加到互联网网关")
            
            # 关联子网和路由表
            self.ec2.associate_route_table(
                SubnetId=subnet_id,
                RouteTableId=route_table_id
            )
            self.logger.info("子网和路由表已关联")
            
            return {
                'vpc_id': vpc_id,
                'subnet_id': subnet_id,
                'internet_gateway_id': igw_id,
                'route_table_id': route_table_id
            }
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'UnauthorizedOperation':
                print("权限错误：当前AWS用户没有足够的权限执行此操作。")
                print("请确保您的用户具有适当的EC2权限，例如AmazonEC2FullAccess策略。")
            elif error_code == 'VpcLimitExceeded':
                print("错误：VPC数量已达上限。")
                print("请删除不需要的VPC或申请提高VPC配额。")
                print("或者使用已有的VPC资源。")
            print(f"创建VPC时出错: {e}")
            return None
        except Exception as e:
            print(f"创建VPC时发生未知错误: {e}")
            return None
    
    def create_security_group(self, vpc_id):
        """创建安全组"""
        try:
            self.logger.info("正在创建安全组...")
            sg_response = self.ec2.create_security_group(
                GroupName='Google-VPN-SG',
                Description='Security group for Google VPN access',
                VpcId=vpc_id
            )
            sg_id = sg_response['GroupId']
            self.logger.info(f"安全组创建成功: {sg_id}")
            
            # 添加入站规则
            self.logger.info("正在配置安全组入站规则...")
            self.ec2.authorize_security_group_ingress(
                GroupId=sg_id,
                IpPermissions=[
                    {
                        'IpProtocol': 'tcp',
                        'FromPort': 22,
                        'ToPort': 22,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    },
                    {
                        'IpProtocol': 'udp',
                        'FromPort': 500,
                        'ToPort': 500,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    },
                    {
                        'IpProtocol': 'udp',
                        'FromPort': 4500,
                        'ToPort': 4500,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    },
                    {
                        'IpProtocol': '50',  # ESP协议
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    }
                ]
            )
            self.logger.info("安全组入站规则配置完成")
            
            # 注意：安全组默认允许所有出站流量，无需额外配置
            
            return sg_id
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'UnauthorizedOperation':
                print("权限错误：当前AWS用户没有足够的权限执行此操作。")
                print("请确保您的用户具有适当的EC2权限，例如AmazonEC2FullAccess策略。")
            print(f"创建安全组时出错: {e}")
            return None
        except Exception as e:
            print(f"创建安全组时发生未知错误: {e}")
            return None
    
    def create_vpn_instance(self, subnet_id, security_group_id):
        """强制复用现有VPN实例，禁止创建新实例"""
        try:
            self.logger.info("🔍 强制复用模式：查找现有VPN实例...")

            # 检查是否已有VPN服务器实例（扩大搜索范围）
            existing_instances = self.ec2.describe_instances(Filters=[
                {'Name': 'tag:Name', 'Values': [
                    'Google-VPN-Server*',
                    '*VPN*',
                    '*vpn*'
                ]},
                {'Name': 'instance-state-name', 'Values': ['running', 'stopped', 'stopping', 'starting']}
            ])

            reusable_instances = []
            for reservation in existing_instances.get('Reservations', []):
                for instance in reservation.get('Instances', []):
                    instance_id = instance['InstanceId']
                    state = instance['State']['Name']
                    public_ip = instance.get('PublicIpAddress')

                    # 获取弹性IP
                    if not public_ip:
                        try:
                            addresses = self.ec2.describe_addresses()
                            for address in addresses.get('Addresses', []):
                                if address.get('InstanceId') == instance_id:
                                    public_ip = address['PublicIp']
                                    break
                        except ClientError:
                            pass

                    # 获取实例名称
                    instance_name = ""
                    for tag in instance.get('Tags', []):
                        if tag['Key'] == 'Name':
                            instance_name = tag['Value']
                            break

                    reusable_instances.append({
                        'id': instance_id,
                        'name': instance_name,
                        'state': state,
                        'public_ip': public_ip,
                        'launch_time': instance['LaunchTime'],
                        'instance_type': instance['InstanceType'],
                        'key_name': instance.get('KeyName'),
                        'vpc_id': instance.get('VpcId'),
                        'subnet_id': instance.get('SubnetId')
                    })

            if not reusable_instances:
                raise Exception("❌ 资源复用策略：未找到可复用的VPN实例，禁止创建新实例！请先手动创建实例或修改复用策略。")

            # 按优先级排序：运行中 > 已停止 > 其他，然后按时间
            reusable_instances.sort(key=lambda x: (
                0 if x['state'] == 'running' else
                1 if x['state'] == 'stopped' else 2,
                x['launch_time']
            ), reverse=True)

            # 选择最佳实例
            best_instance = reusable_instances[0]
            instance_id = best_instance['id']

            print(f"✅ 复用现有实例: {instance_id} ({best_instance['name']})")
            print(f"   状态: {best_instance['state']}, 类型: {best_instance['instance_type']}")

            # 清理其他重复实例
            if len(reusable_instances) > 1:
                duplicate_ids = [inst['id'] for inst in reusable_instances[1:]]
                print(f"🧹 清理 {len(duplicate_ids)} 个重复实例...")
                for inst in reusable_instances[1:]:
                    print(f"   删除: {inst['id']} ({inst['name']}) - {inst['state']}")

                try:
                    self.ec2.terminate_instances(InstanceIds=duplicate_ids)
                    print("✅ 重复实例已标记为终止")
                except Exception as e:
                    print(f"⚠️ 清理重复实例失败: {e}")

            # 如果实例已停止，启动它
            if best_instance['state'] == 'stopped':
                print("🔄 启动已停止的实例...")
                self.ec2.start_instances(InstanceIds=[instance_id])

                # 等待实例运行
                waiter = self.ec2.get_waiter('instance_running')
                waiter.wait(InstanceIds=[instance_id])
                print("✅ 实例已启动")

                # 重新获取公网IP
                response = self.ec2.describe_instances(InstanceIds=[instance_id])
                instance = response['Reservations'][0]['Instances'][0]
                best_instance['public_ip'] = instance.get('PublicIpAddress')

            public_ip = best_instance['public_ip']
            print(f"   公网IP: {public_ip}")

            # 确保有弹性IP
            if not public_ip:
                print("🔄 分配弹性IP...")
                try:
                    allocation = self.ec2.allocate_address(Domain='vpc')
                    self.ec2.associate_address(
                        InstanceId=instance_id,
                        AllocationId=allocation['AllocationId']
                    )
                    public_ip = allocation['PublicIp']
                    print(f"✅ 弹性IP已分配: {public_ip}")
                except Exception as e:
                    print(f"⚠️ 分配弹性IP失败: {e}")

            # 返回复用的实例信息
            return {
                'instance_id': instance_id,
                'public_ip': public_ip,
                'instance_type': best_instance['instance_type'],
                'key_name': best_instance['key_name'],
                'reused': True
            }

        except Exception as e:
            self.logger.error(f"复用VPN实例失败: {e}")
            raise Exception(f"❌ 资源复用失败: {e}")

    def create_new_vpn_instance(self, subnet_id, security_group_id):
        """创建新VPN实例的完整参数配置（备用方法）"""
        try:
            # 获取AMI ID
            ami_id = self.get_ami_id(with_gui=False)
            if not ami_id:
                raise Exception("无法获取AMI ID")

            # 完整的实例创建参数
            instance_params = {
                'ImageId': ami_id,
                'MinCount': 1,
                'MaxCount': 1,
                'InstanceType': 't3.micro',  # 默认使用最小实例类型
                'KeyName': 'Google-VPN-Key',
                'SecurityGroupIds': [security_group_id],
                'SubnetId': subnet_id,
                'AssociatePublicIpAddress': True,
                'IamInstanceProfile': {
                    'Name': 'EC2-SSM-Role'  # 支持Session Manager
                },
                'BlockDeviceMappings': [
                    {
                        'DeviceName': '/dev/xvda',
                        'Ebs': {
                            'VolumeSize': 8,  # 8GB存储
                            'VolumeType': 'gp3',  # 使用gp3更经济
                            'DeleteOnTermination': True,
                            'Encrypted': False
                        }
                    }
                ],
                'TagSpecifications': [
                    {
                        'ResourceType': 'instance',
                        'Tags': [
                            {'Key': 'Name', 'Value': 'Google-VPN-Server-New'},
                            {'Key': 'Type', 'Value': 'VPN-Server'},
                            {'Key': 'Project', 'Value': 'Network-API'},
                            {'Key': 'CostOptimized', 'Value': 'true'},
                            {'Key': 'AutoManaged', 'Value': 'true'},
                            {'Key': 'CreatedBy', 'Value': 'vpn_deployment.py'},
                            {'Key': 'CreatedDate', 'Value': str(datetime.now().date())}
                        ]
                    }
                ],
                'UserData': self._get_user_data_script(),
                'Monitoring': {'Enabled': False},  # 禁用详细监控以节省费用
                'DisableApiTermination': False,
                'InstanceInitiatedShutdownBehavior': 'stop',
                'EbsOptimized': False,  # t3.micro不支持EBS优化
                'CreditSpecification': {
                    'CpuCredits': 'standard'  # 标准CPU积分
                }
            }

            print("⚠️ 注意：此方法仅为备用，当前策略禁止创建新实例")
            print("如需创建新实例，请修改资源复用策略")

            # 实际不执行创建，只返回参数配置
            return {
                'method': 'create_new_instance',
                'params': instance_params,
                'note': '完整参数配置已准备，但当前策略禁止创建新实例'
            }

        except Exception as e:
            self.logger.error(f"准备实例创建参数失败: {e}")
            return None

    def _get_user_data_script(self):
        """获取用户数据脚本"""
        user_data = """#!/bin/bash
# VPN服务器初始化脚本
yum update -y
yum install -y libreswan xl2tpd

# 配置IP转发
echo 'net.ipv4.ip_forward = 1' >> /etc/sysctl.conf
sysctl -p

# 启用服务
systemctl enable ipsec
systemctl enable xl2tpd

# 创建基本配置
mkdir -p /etc/ipsec.d/{cacerts,certs,private}

# 设置SSH密钥
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chown ec2-user:ec2-user /home/<USER>/.ssh

# 安装Session Manager
yum install -y amazon-ssm-agent
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent

echo "VPN服务器初始化完成" > /var/log/vpn-init.log
"""
        import base64
        return base64.b64encode(user_data.encode()).decode()

    def get_ami_id(self, with_gui=True):
        """获取AMI ID，支持GUI界面"""
        try:
            if with_gui:
                # 查找Ubuntu Desktop AMI（支持GUI）
                print("🔍 查找支持GUI的Ubuntu Desktop AMI...")
                response = self.ec2.describe_images(
                    Filters=[
                        {'Name': 'name', 'Values': ['ubuntu/images/hvm-ssd/ubuntu-22.04-desktop-amd64-*']},
                        {'Name': 'state', 'Values': ['available']},
                        {'Name': 'architecture', 'Values': ['x86_64']},
                        {'Name': 'virtualization-type', 'Values': ['hvm']},
                        {'Name': 'root-device-type', 'Values': ['ebs']}
                    ],
                    Owners=['099720109477']  # Canonical
                )

                if response['Images']:
                    # 选择最新的AMI
                    latest_ami = sorted(response['Images'], key=lambda x: x['CreationDate'], reverse=True)[0]
                    ami_id = latest_ami['ImageId']
                    print(f"✅ 找到Ubuntu Desktop AMI: {ami_id} ({latest_ami['Name']})")
                    return ami_id
                else:
                    print("⚠️ 未找到Ubuntu Desktop AMI，使用Amazon Linux 2")

            # 备用：使用Amazon Linux 2
            print("🔍 查找Amazon Linux 2 AMI...")
            response = self.ec2.describe_images(
                Filters=[
                    {'Name': 'name', 'Values': ['amzn2-ami-hvm-*-x86_64-gp2']},
                    {'Name': 'state', 'Values': ['available']},
                    {'Name': 'architecture', 'Values': ['x86_64']},
                    {'Name': 'virtualization-type', 'Values': ['hvm']},
                    {'Name': 'root-device-type', 'Values': ['ebs']}
                ],
                Owners=['amazon']
            )

            if response['Images']:
                latest_ami = sorted(response['Images'], key=lambda x: x['CreationDate'], reverse=True)[0]
                ami_id = latest_ami['ImageId']
                print(f"✅ 找到Amazon Linux 2 AMI: {ami_id}")
                return ami_id

            # 最后的备用选项
            ami_mapping = {
                'us-east-1': 'ami-09d95fab7fff3776c',
                'us-west-2': 'ami-008fe2fc65df48dac',
                'eu-west-1': 'ami-0dad359ff462124ca'
            }
            return ami_mapping.get(self.region, 'ami-09d95fab7fff3776c')

        except Exception as e:
            print(f"⚠️ 获取AMI时出错: {e}")
            # 使用备用AMI
            ami_mapping = {
                'us-east-1': 'ami-09d95fab7fff3776c',
                'us-west-2': 'ami-008fe2fc65df48dac',
                'eu-west-1': 'ami-0dad359ff462124ca'
            }
            return ami_mapping.get(self.region, 'ami-09d95fab7fff3776c')
    
    def create_key_pair(self):
        """创建密钥对"""
        try:
            key_name = 'Google-VPN-Key'
            # 检查密钥对是否已存在
            try:
                self.ec2.describe_key_pairs(KeyNames=[key_name])
                self.logger.info(f"密钥对 {key_name} 已存在")
                return key_name
            except ClientError:
                pass
            
            # 创建新密钥对
            self.logger.info(f"创建新的密钥对: {key_name}")
            key_response = self.ec2.create_key_pair(KeyName=key_name)
            # 实际应用中应该保存私钥内容到安全位置
            with open(f'{key_name}.pem', 'w') as f:
                f.write(key_response['KeyMaterial'])
            
            return key_name
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'UnauthorizedOperation':
                print("权限错误：当前AWS用户没有足够的权限执行此操作。")
                print("请确保您的用户具有适当的EC2权限，例如AmazonEC2FullAccess策略。")
            print(f"创建密钥对时出错: {e}")
            return None
        except Exception as e:
            print(f"创建密钥对时发生未知错误: {e}")
            return None
    
    def get_vpn_setup_script(self):
        """获取增强的多协议VPN服务器设置脚本"""
        # 从配置文件中读取预共享密钥
        try:
            psk = self.config.get('VPN', 'PreSharedKey')
        except (configparser.NoSectionError, configparser.NoOptionError):
            self.logger.warning("未找到配置文件或预共享密钥，使用默认值")
            psk = "GoogleVPN2023!"

        script = f'''#!/bin/bash
exec > /var/log/vpn-setup.log 2>&1
echo "开始配置VPN服务器（支持GUI和Chrome）..."

# 从EC2元数据动态获取公网IP
TOKEN=`curl -s -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600"`
PUBLIC_IP=`curl -s -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/public-ipv4`
echo "动态获取到服务器公网IP: $PUBLIC_IP"

# 更新系统并安装基础软件
yum update -y
amazon-linux-extras install -y epel
yum install -y strongswan xl2tpd ppp net-tools curl wget

# 安装Chrome浏览器
cat > /etc/yum.repos.d/google-chrome.repo << 'EOF'
[google-chrome]
name=google-chrome
baseurl=http://dl.google.com/linux/chrome/rpm/stable/x86_64
enabled=1
gpgcheck=1
gpgkey=https://dl.google.com/linux/linux_signing_key.pub
EOF
yum install -y google-chrome-stable 2>/dev/null || echo "Chrome安装跳过"

# 配置防火墙（智能SSH保护）
systemctl stop firewalld 2>/dev/null || true
iptables -F && iptables -t nat -F
iptables -P INPUT ACCEPT && iptables -P FORWARD ACCEPT && iptables -P OUTPUT ACCEPT

# SSH智能保护：VPN客户端无限制，其他限制
iptables -A INPUT -i lo -j ACCEPT
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
iptables -A INPUT -s **********/24 -p tcp --dport 22 -j ACCEPT
iptables -A INPUT -s **********/24 -p tcp --dport 22 -j ACCEPT
iptables -A INPUT -p tcp --dport 22 -m recent --set --name SSH
iptables -A INPUT -p tcp --dport 22 -m recent --update --seconds 300 --hitcount 10 --name SSH -j DROP
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# VPN端口
iptables -A INPUT -p udp -m multiport --dports 500,4500,1701 -j ACCEPT
iptables -A INPUT -p 50 -j ACCEPT

# NAT和转发
iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
iptables -A FORWARD -s **********/24 -j ACCEPT
iptables -A FORWARD -s **********/24 -j ACCEPT

# 配置系统参数 - 完全开放VPN流量
cat > /etc/sysctl.conf << 'SYSCTL_EOF'
net.ipv4.ip_forward = 1
net.ipv6.conf.all.forwarding = 1
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.all.rp_filter = 0
net.ipv4.conf.eth0.rp_filter = 0
net.core.rmem_max = *********
net.core.wmem_max = *********
net.ipv4.tcp_rmem = 4096 65536 *********
net.ipv4.tcp_wmem = 4096 65536 *********
net.ipv4.tcp_keepalive_time = 600
net.ipv4.tcp_keepalive_intvl = 60
net.ipv4.tcp_keepalive_probes = 3
net.netfilter.nf_conntrack_max = 1048576
net.netfilter.nf_conntrack_tcp_timeout_established = 7200
SYSCTL_EOF
sysctl -p

# 配置StrongSwan主配置文件
cat > /etc/strongswan/strongswan.conf << 'STRONGSWAN_EOF'
charon {{
    load_modular = yes
    plugins {{
        include strongswan.d/charon/*.conf
    }}
    threads = 16
}}
include strongswan.d/*.conf
STRONGSWAN_EOF

# 配置StrongSwan - 支持所有VPN客户端，无任何限制
cat > /etc/strongswan/ipsec.conf << 'IPSEC_EOF'
config setup
    charondebug="ike 1, knl 1, cfg 0"
    uniqueids=no
    strictcrlpolicy=no

conn %default
    left=%defaultroute
    leftsubnet=0.0.0.0/0
    right=%any
    rightsourceip=**********/24
    rightdns=*******,*******
    auto=add
    keyexchange=ikev2
    # 最大兼容性加密算法，支持所有客户端
    ike=aes256-sha256-modp2048,aes256-sha1-modp2048,aes128-sha256-modp2048,aes128-sha1-modp2048,aes256-sha1-modp1024,aes128-sha1-modp1024,3des-sha1-modp1024,aes256-sha256-ecp256,aes128-sha256-ecp256!
    esp=aes256-sha256,aes256-sha1,aes128-sha256,aes128-sha1,3des-sha1,aes256gcm16,aes128gcm16!
    dpdaction=clear
    dpddelay=300s
    rekey=no
    leftfirewall=yes
    compress=yes
    mobike=yes
    fragmentation=yes

# EAP认证连接（支持用户名密码）
conn Google-VPN-EAP
    also=%default
    leftauth=psk
    rightauth=eap-mschapv2
    rightsubnet=0.0.0.0/0
    leftid=$PUBLIC_IP
    rightid=%any
    eap_identity=%identity

# PSK认证连接
conn Google-VPN-PSK
    also=%default
    leftauth=psk
    rightauth=psk
    rightsubnet=0.0.0.0/0
    leftid=************
    rightid=%any

# L2TP连接
conn L2TP-PSK
    also=%default
    leftauth=psk
    rightauth=psk
    type=transport
    leftprotoport=17/1701
    rightprotoport=17/%any
    rightsubnet=vhost:%priv
    leftid=$PUBLIC_IP
    rightid=%any
IPSEC_EOF

# 配置认证密钥
cat > /etc/strongswan/ipsec.secrets << 'SECRETS_EOF'
# PSK认证
: PSK "{psk}"
$PUBLIC_IP %any : PSK "{psk}"

# EAP用户认证
vpnuser : EAP "{psk}"
testuser : EAP "{psk}"
admin : EAP "{psk}"
SECRETS_EOF

# 配置L2TP/IPSec
cat > /etc/xl2tpd/xl2tpd.conf << 'L2TP_EOF'
[global]
listen-addr = 0.0.0.0
port = 1701
auth file = /etc/ppp/chap-secrets

[lns default]
ip range = ***********-************
local ip = **********
require chap = yes
refuse pap = yes
require authentication = yes
name = l2tpd
ppp debug = yes
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
L2TP_EOF

cat > /etc/ppp/options.xl2tpd << 'PPP_EOF'
ipcp-accept-local
ipcp-accept-remote
ms-dns *******
ms-dns *******
noccp
auth
crtscts
idle 1800
mtu 1410
mru 1410
nodefaultroute
debug
lock
proxyarp
connect-delay 5000
require-mschap-v2
PPP_EOF

cat > /etc/ppp/chap-secrets << 'CHAP_EOF'
vpnuser l2tpd {psk} *
testuser l2tpd {psk} *
admin l2tpd {psk} *
CHAP_EOF

# 注意：防火墙规则已在前面设置为完全开放
# 这里只保存当前的开放状态
iptables-save > /etc/iptables.rules

# 配置SSH（VPN友好，防攻击）
cat > /etc/ssh/sshd_config << 'EOF'
Port 22
Protocol 2
PermitRootLogin no
PubkeyAuthentication yes
PasswordAuthentication no
UsePAM yes
ClientAliveInterval 60
ClientAliveCountMax 10
TCPKeepAlive yes
MaxAuthTries 10
MaxSessions 20
MaxStartups 20:30:60
UseDNS no
AllowUsers ec2-user
PermitUserEnvironment no
PermitTunnel no
StrictModes yes
EOF

systemctl restart sshd
systemctl enable sshd

# 启动VPN服务
systemctl enable strongswan
systemctl start strongswan
systemctl enable xl2tpd
systemctl start xl2tpd

# 创建开机自启服务（确保重启后保持完全开放状态）
cat > /etc/systemd/system/vpn-no-firewall.service << 'SERVICE_EOF'
[Unit]
Description=Ensure VPN firewall stays open
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'iptables -P INPUT ACCEPT; iptables -P FORWARD ACCEPT; iptables -P OUTPUT ACCEPT; iptables -F; iptables -X; iptables -t nat -F; iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE; iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE; iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE'
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
SERVICE_EOF

systemctl enable vpn-no-firewall

# 创建监控脚本
cat > /usr/local/bin/vpn-monitor.sh << 'EOF'
#!/bin/bash
# VPN服务监控
for service in sshd strongswan xl2tpd; do
    systemctl is-active --quiet $service || systemctl restart $service
done
# 确保NAT规则
iptables -t nat -L | grep -q MASQUERADE || iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
EOF

chmod +x /usr/local/bin/vpn-monitor.sh

# 添加到定时任务
echo "*/2 * * * * root /usr/local/bin/vpn-monitor.sh" > /etc/cron.d/vpn-monitor

echo "🎉 VPN服务器配置完成（支持GUI和Chrome）！"
echo "协议: IKEv2 (EAP + PSK), L2TP/IPSec"
echo "安全: 智能SSH防护，VPN客户端无限制"
echo "密钥: {psk}"
echo "用户: vpnuser/testuser/admin"
echo "密码: {psk}"
echo "支持: Mac/Windows/iOS/Android + Chrome浏览器"
'''
        import base64
        return base64.b64encode(script.encode()).decode()
    
    def deploy_vpn(self):
        """部署完整的VPN解决方案"""
        self.logger.info("开始部署Google VPN服务...")
        
        # 首先检查是否有可用的现有VPC资源
        vpc_info = self.get_existing_vpc_resources()
        if vpc_info:
            self.logger.info("使用现有的VPC资源")
        else:
            # 创建VPC基础设施
            self.logger.info("1. 创建VPC基础设施...")
            vpc_info = self.create_vpc()
            if not vpc_info:
                self.logger.error("VPC创建失败")
                return False
        
        # 如果没有现有的安全组，则创建安全组
        if 'security_group_id' not in vpc_info:
            self.logger.info("2. 创建安全组...")
            sg_id = self.create_security_group(vpc_info['vpc_id'])
            if not sg_id:
                self.logger.error("安全组创建失败")
                return False
            vpc_info['security_group_id'] = sg_id
        else:
            self.logger.info("2. 使用现有的安全组...")
        
        # 创建VPN实例
        self.logger.info("3. 创建VPN实例...")
        instance_info = self.create_vpn_instance(vpc_info['subnet_id'], vpc_info['security_group_id'])
        if not instance_info:
            self.logger.error("VPN实例创建失败")
            return False
        
        self.logger.info(f"VPN部署完成!")
        self.logger.info(f"VPN服务器公网IP: {instance_info['public_ip']}")
        
        # 从配置文件或使用默认值获取预共享密钥
        psk = self.config.get('VPN', 'PreSharedKey', fallback='GoogleVPN2023!')
        self.logger.info(f"预共享密钥: {psk}")
        self.logger.info(f"客户端配置建议:")
        self.logger.info(f"  - 服务器地址: {instance_info['public_ip']}")
        self.logger.info(f"  - IPSec类型: IKEv2")
        self.logger.info(f"  - 认证方式: 预共享密钥")
        self.logger.info(f"  - 远程ID: {instance_info['public_ip']}")
        self.logger.info(f"  - 本地ID: (留空)")
        
        # 保存部署信息到文件
        deployment_info = {
            'instance_id': instance_info['instance_id'],
            'public_ip': instance_info['public_ip'],
            'vpc_id': vpc_info['vpc_id'],
            'subnet_id': vpc_info['subnet_id'],
            'security_group_id': vpc_info['security_group_id'],
            'region': self.region
        }
        
        with open('vpn_deployment_info.json', 'w') as f:
            json.dump(deployment_info, f, indent=2)
        
        self.logger.info("部署信息已保存到 vpn_deployment_info.json")
        
        return True

def main():
    print("Network-API VPN 部署工具")
    print("========================")
    
    # 检查AWS凭证
    try:
        sts = boto3.client('sts')
        identity = sts.get_caller_identity()
        print(f"AWS账户ID: {identity['Account']}")
        print(f"用户ARN: {identity['Arn']}")
    except Exception as e:
        print(f"错误：无法验证AWS凭证: {e}")
        print("请确保已正确配置AWS凭证。")
        return
    
    # 创建部署实例
    vpn_deploy = GoogleVPNDeployment()
    
    # 开始部署
    success = vpn_deploy.deploy_vpn()
    
    if success:
        print("\nVPN部署成功完成！")
        print("请查看日志和 vpn_deployment_info.json 文件获取详细信息。")
    else:
        print("\nVPN部署失败！")
        print("请查看错误信息并解决后重试。")

if __name__ == "__main__":
    main()
