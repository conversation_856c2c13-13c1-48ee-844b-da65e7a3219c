#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证VPN部署信息脚本
检查部署信息文件中记录的AWS资源是否存在且状态正常
"""

import boto3
import json
import sys
from botocore.exceptions import ClientError

def verify_deployment():
    print("VPN部署信息验证工具")
    print("==================")
    
    # 读取部署信息
    try:
        with open('vpn_deployment_info.json', 'r', encoding='utf-8') as f:
            deployment_info = json.load(f)
        print("✅ 成功读取部署信息文件")
    except FileNotFoundError:
        print("❌ 错误：未找到部署信息文件 vpn_deployment_info.json")
        return False
    except Exception as e:
        print(f"❌ 错误：读取部署信息文件失败: {e}")
        return False
    
    # 显示部署信息
    print("\n记录的部署信息:")
    print(f"  实例ID: {deployment_info.get('instance_id')}")
    print(f"  公网IP: {deployment_info.get('public_ip')}")
    print(f"  VPC ID: {deployment_info.get('vpc_id')}")
    print(f"  子网ID: {deployment_info.get('subnet_id')}")
    print(f"  安全组ID: {deployment_info.get('security_group_id')}")
    print(f"  区域: {deployment_info.get('region')}")
    
    # 初始化AWS客户端
    try:
        region = deployment_info.get('region', 'us-east-1')
        ec2 = boto3.client('ec2', region_name=region)
        print(f"\n✅ AWS客户端初始化成功 (区域: {region})")
    except Exception as e:
        print(f"❌ 错误：无法初始化AWS客户端: {e}")
        return False
    
    verification_results = {}
    
    # 1. 验证EC2实例
    print("\n=== 验证EC2实例 ===")
    instance_id = deployment_info.get('instance_id')
    if instance_id:
        try:
            response = ec2.describe_instances(InstanceIds=[instance_id])
            if response['Reservations']:
                instance = response['Reservations'][0]['Instances'][0]
                state = instance['State']['Name']
                public_ip = instance.get('PublicIpAddress', 'N/A')
                private_ip = instance.get('PrivateIpAddress', 'N/A')
                instance_type = instance.get('InstanceType', 'N/A')
                
                print(f"✅ 实例 {instance_id} 存在")
                print(f"   状态: {state}")
                print(f"   公网IP: {public_ip}")
                print(f"   私网IP: {private_ip}")
                print(f"   实例类型: {instance_type}")
                
                # 检查IP是否匹配
                recorded_ip = deployment_info.get('public_ip')
                if public_ip == recorded_ip:
                    print(f"✅ 公网IP匹配记录: {public_ip}")
                else:
                    print(f"⚠️  公网IP不匹配 - 记录: {recorded_ip}, 实际: {public_ip}")
                
                verification_results['instance'] = {
                    'exists': True,
                    'state': state,
                    'ip_match': public_ip == recorded_ip
                }
            else:
                print(f"❌ 实例 {instance_id} 不存在")
                verification_results['instance'] = {'exists': False}
        except ClientError as e:
            print(f"❌ 验证实例时出错: {e}")
            verification_results['instance'] = {'exists': False, 'error': str(e)}
    
    # 2. 验证VPC
    print("\n=== 验证VPC ===")
    vpc_id = deployment_info.get('vpc_id')
    if vpc_id:
        try:
            response = ec2.describe_vpcs(VpcIds=[vpc_id])
            if response['Vpcs']:
                vpc = response['Vpcs'][0]
                state = vpc['State']
                cidr = vpc['CidrBlock']
                
                print(f"✅ VPC {vpc_id} 存在")
                print(f"   状态: {state}")
                print(f"   CIDR块: {cidr}")
                
                verification_results['vpc'] = {
                    'exists': True,
                    'state': state,
                    'cidr': cidr
                }
            else:
                print(f"❌ VPC {vpc_id} 不存在")
                verification_results['vpc'] = {'exists': False}
        except ClientError as e:
            print(f"❌ 验证VPC时出错: {e}")
            verification_results['vpc'] = {'exists': False, 'error': str(e)}
    
    # 3. 验证子网
    print("\n=== 验证子网 ===")
    subnet_id = deployment_info.get('subnet_id')
    if subnet_id:
        try:
            response = ec2.describe_subnets(SubnetIds=[subnet_id])
            if response['Subnets']:
                subnet = response['Subnets'][0]
                state = subnet['State']
                cidr = subnet['CidrBlock']
                az = subnet['AvailabilityZone']
                
                print(f"✅ 子网 {subnet_id} 存在")
                print(f"   状态: {state}")
                print(f"   CIDR块: {cidr}")
                print(f"   可用区: {az}")
                
                verification_results['subnet'] = {
                    'exists': True,
                    'state': state,
                    'cidr': cidr,
                    'az': az
                }
            else:
                print(f"❌ 子网 {subnet_id} 不存在")
                verification_results['subnet'] = {'exists': False}
        except ClientError as e:
            print(f"❌ 验证子网时出错: {e}")
            verification_results['subnet'] = {'exists': False, 'error': str(e)}
    
    # 4. 验证安全组
    print("\n=== 验证安全组 ===")
    sg_id = deployment_info.get('security_group_id')
    if sg_id:
        try:
            response = ec2.describe_security_groups(GroupIds=[sg_id])
            if response['SecurityGroups']:
                sg = response['SecurityGroups'][0]
                name = sg['GroupName']
                description = sg['Description']
                vpc_id_sg = sg['VpcId']
                
                print(f"✅ 安全组 {sg_id} 存在")
                print(f"   名称: {name}")
                print(f"   描述: {description}")
                print(f"   VPC ID: {vpc_id_sg}")
                
                # 检查VPC是否匹配
                if vpc_id_sg == vpc_id:
                    print(f"✅ 安全组VPC匹配: {vpc_id_sg}")
                else:
                    print(f"⚠️  安全组VPC不匹配 - 期望: {vpc_id}, 实际: {vpc_id_sg}")
                
                verification_results['security_group'] = {
                    'exists': True,
                    'name': name,
                    'vpc_match': vpc_id_sg == vpc_id
                }
            else:
                print(f"❌ 安全组 {sg_id} 不存在")
                verification_results['security_group'] = {'exists': False}
        except ClientError as e:
            print(f"❌ 验证安全组时出错: {e}")
            verification_results['security_group'] = {'exists': False, 'error': str(e)}
    
    # 5. 生成验证总结
    print("\n" + "="*50)
    print("验证总结")
    print("="*50)
    
    all_good = True
    
    if verification_results.get('instance', {}).get('exists'):
        if verification_results['instance'].get('state') == 'running':
            print("✅ EC2实例: 存在且运行中")
        else:
            print(f"⚠️  EC2实例: 存在但状态为 {verification_results['instance'].get('state')}")
            all_good = False
    else:
        print("❌ EC2实例: 不存在或验证失败")
        all_good = False
    
    if verification_results.get('vpc', {}).get('exists'):
        print("✅ VPC: 存在且可用")
    else:
        print("❌ VPC: 不存在或验证失败")
        all_good = False
    
    if verification_results.get('subnet', {}).get('exists'):
        print("✅ 子网: 存在且可用")
    else:
        print("❌ 子网: 不存在或验证失败")
        all_good = False
    
    if verification_results.get('security_group', {}).get('exists'):
        print("✅ 安全组: 存在且配置正确")
    else:
        print("❌ 安全组: 不存在或验证失败")
        all_good = False
    
    if all_good:
        print("\n🎉 所有资源验证通过，部署信息一致！")
    else:
        print("\n⚠️  部分资源验证失败，请检查部署状态")
    
    return all_good

if __name__ == "__main__":
    verify_deployment()
