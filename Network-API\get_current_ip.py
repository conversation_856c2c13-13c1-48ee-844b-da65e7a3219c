#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取当前VPN实例的实际IP地址并更新配置文件
"""

import boto3
import json
import os
import configparser
from botocore.exceptions import ClientError

def get_current_instance_ip():
    """获取当前实例的实际IP地址"""
    try:
        # 读取部署信息
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        instance_id = deployment_info['instance_id']
        region = deployment_info['region']
        
        # 连接AWS
        ec2 = boto3.client('ec2', region_name=region)
        
        # 获取实例信息
        response = ec2.describe_instances(InstanceIds=[instance_id])
        
        if not response['Reservations']:
            print(f"❌ 实例 {instance_id} 不存在")
            return None
        
        instance = response['Reservations'][0]['Instances'][0]
        current_public_ip = instance.get('PublicIpAddress')
        current_private_ip = instance.get('PrivateIpAddress')
        state = instance['State']['Name']
        
        print(f"🔍 实例 {instance_id} 状态检查:")
        print(f"   状态: {state}")
        print(f"   当前公网IP: {current_public_ip}")
        print(f"   当前私网IP: {current_private_ip}")
        print(f"   配置文件中的IP: {deployment_info['public_ip']}")
        
        if current_public_ip != deployment_info['public_ip']:
            print(f"⚠️ IP地址已变化！")
            print(f"   旧IP: {deployment_info['public_ip']}")
            print(f"   新IP: {current_public_ip}")
            return {
                'instance_id': instance_id,
                'old_ip': deployment_info['public_ip'],
                'new_ip': current_public_ip,
                'private_ip': current_private_ip,
                'state': state,
                'needs_update': True
            }
        else:
            print(f"✅ IP地址未变化")
            return {
                'instance_id': instance_id,
                'current_ip': current_public_ip,
                'private_ip': current_private_ip,
                'state': state,
                'needs_update': False
            }
            
    except Exception as e:
        print(f"❌ 获取实例IP失败: {e}")
        return None

def update_deployment_info(ip_info):
    """更新部署信息文件"""
    if not ip_info['needs_update']:
        return True
        
    try:
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        # 更新IP地址
        deployment_info['public_ip'] = ip_info['new_ip']
        deployment_info['private_ip'] = ip_info['private_ip']
        deployment_info['state'] = ip_info['state']
        
        # 备份原文件
        with open('vpn_deployment_info.json.backup', 'w') as f:
            json.dump(deployment_info, f, indent=2)
        
        # 写入新文件
        with open('vpn_deployment_info.json', 'w') as f:
            json.dump(deployment_info, f, indent=2)
        
        print(f"✅ 已更新 vpn_deployment_info.json")
        return True
        
    except Exception as e:
        print(f"❌ 更新部署信息失败: {e}")
        return False

def update_vpn_config(ip_info):
    """更新VPN配置文件"""
    if not ip_info['needs_update']:
        return True
        
    try:
        config = configparser.ConfigParser()
        config.read('vpn_config.ini')
        
        # 更新服务器IP
        if 'VPN' not in config:
            config['VPN'] = {}
        config['VPN']['ServerIP'] = ip_info['new_ip']
        
        # 备份原文件
        with open('vpn_config.ini.backup', 'w') as f:
            config.write(f)
        
        # 写入新文件
        with open('vpn_config.ini', 'w') as f:
            config.write(f)
        
        print(f"✅ 已更新 vpn_config.ini")
        return True
        
    except Exception as e:
        print(f"❌ 更新VPN配置失败: {e}")
        return False

def update_documentation_files(ip_info):
    """更新文档文件中的IP地址"""
    if not ip_info['needs_update']:
        return True
    
    files_to_update = [
        'README.md',
        'VPN客户端配置指南.md',
        '项目最新状态.md'
    ]
    
    old_ip = ip_info['old_ip']
    new_ip = ip_info['new_ip']
    
    for filename in files_to_update:
        if os.path.exists(filename):
            try:
                # 读取文件
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 替换IP地址
                if old_ip in content:
                    # 备份原文件
                    with open(f"{filename}.backup", 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    # 更新内容
                    updated_content = content.replace(old_ip, new_ip)
                    
                    # 写入新文件
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(updated_content)
                    
                    print(f"✅ 已更新 {filename}")
                else:
                    print(f"ℹ️ {filename} 中未找到旧IP地址")
                    
            except Exception as e:
                print(f"❌ 更新 {filename} 失败: {e}")
    
    return True

def main():
    print("=== VPN实例IP地址检查和更新工具 ===")
    
    # 1. 获取当前IP
    ip_info = get_current_instance_ip()
    if not ip_info:
        return
    
    if not ip_info['needs_update']:
        print("✅ 所有配置都是最新的，无需更新")
        return
    
    print(f"\n🔧 开始更新配置文件...")
    
    # 2. 更新部署信息
    update_deployment_info(ip_info)
    
    # 3. 更新VPN配置
    update_vpn_config(ip_info)
    
    # 4. 更新文档文件
    update_documentation_files(ip_info)
    
    print(f"\n✅ 配置更新完成！")
    print(f"   新的VPN服务器地址: {ip_info['new_ip']}")
    print(f"   请使用新地址进行VPN连接测试")

if __name__ == "__main__":
    main()
