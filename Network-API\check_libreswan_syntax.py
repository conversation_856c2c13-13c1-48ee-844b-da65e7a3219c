#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查libreswan语法和示例
"""

import boto3
import json
import time

def send_command(ssm, instance_id, commands, description):
    """发送单个命令"""
    try:
        print(f"\n🔧 {description}")
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={'commands': commands},
            TimeoutSeconds=120
        )
        
        command_id = response['Command']['CommandId']
        
        # 等待命令完成
        for i in range(30):
            time.sleep(4)
            try:
                result = ssm.get_command_invocation(
                    CommandId=command_id,
                    InstanceId=instance_id
                )
                
                status = result['Status']
                if status == 'Success':
                    output = result['StandardOutputContent']
                    if output.strip():
                        print(f"✅ 输出:\n{output}")
                    else:
                        print("✅ 成功")
                    return True
                elif status == 'Failed':
                    error = result['StandardErrorContent']
                    print(f"❌ 失败: {error}")
                    return False
                elif status in ['InProgress', 'Pending']:
                    continue
                    
            except Exception as e:
                if 'InvocationDoesNotExist' in str(e):
                    continue
                else:
                    print(f"❌ 错误: {e}")
                    return False
        
        print("⚠️ 超时")
        return False
        
    except Exception as e:
        print(f"❌ 发送命令失败: {e}")
        return False

def main():
    try:
        # 读取部署信息
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        instance_id = deployment_info['instance_id']
        region = deployment_info['region']
        
        print(f"实例ID: {instance_id}")
        
        # 创建SSM客户端
        ssm = boto3.client('ssm', region_name=region)
        
        # 检查libreswan文档和示例
        send_command(ssm, instance_id, [
            'man ipsec.conf | head -50',
            'ls /usr/share/doc/libreswan/',
            'find /usr/share/doc/libreswan/ -name "*.conf" -o -name "*example*"'
        ], "检查libreswan文档")
        
        # 检查默认配置
        send_command(ssm, instance_id, [
            'ls /etc/ipsec.d/',
            'cat /etc/ipsec.d/policies/clear',
            'cat /etc/ipsec.d/policies/private'
        ], "检查默认配置")
        
        # 创建最简单的配置
        send_command(ssm, instance_id, [
            'TOKEN=$(curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")',
            'PUBLIC_IP=$(curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/public-ipv4)',
            'cat > /etc/ipsec.conf << EOF',
            'version 2.0',
            '',
            'config setup',
            '    nat_traversal=yes',
            '    virtual_private=%v4:10.0.0.0/8,%v4:***********/16,%v4:**********/12',
            '    oe=off',
            '    protostack=netkey',
            '',
            'conn vpn',
            '    auto=add',
            '    type=tunnel',
            '    ikev2=insist',
            '    left=%defaultroute',
            '    leftid=$PUBLIC_IP',
            '    leftauth=secret',
            '    leftsubnet=0.0.0.0/0',
            '    right=%any',
            '    rightauth=secret',
            '    rightsourceip=**********/24',
            'EOF'
        ], "创建最简单配置")
        
        # 验证简单配置
        send_command(ssm, instance_id, [
            'ipsec addconn --config /etc/ipsec.conf --checkconfig'
        ], "验证简单配置")
        
        print("\n✅ libreswan语法检查完成！")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    print("=== libreswan语法检查工具 ===")
    main()
