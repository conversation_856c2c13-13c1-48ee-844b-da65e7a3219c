# VPN连接指南

🔧 VPN服务器信息:
服务器地址: *************
支持协议: IKEv2 (EAP + PSK), L2TP/IPSec

🔑 认证信息:
用户名: vpnuser
密码: GoogleVPN2023!
预共享密钥: GoogleVPN2023!

📱 推荐连接方式:

1. IKEv2 + EAP认证（用户名密码）⭐ 推荐
   - Windows: 设置 → 网络和Internet → VPN → 添加VPN连接
   - VPN类型: IKEv2
   - 认证: 用户名和密码
   - 用户名: vpnuser
   - 密码: GoogleVPN2023!

2. L2TP/IPSec（兼容性最佳）
   - Windows: 设置 → 网络和Internet → VPN → 添加VPN连接
   - VPN类型: L2TP/IPSec (预共享密钥)
   - 预共享密钥: GoogleVPN2023!
   - 用户名: vpnuser
   - 密码: GoogleVPN2023!

✅ 连接验证:
1. 连接后访问: https://whatismyipaddress.com
2. 应显示IP: ************
3. 测试Google服务: https://www.google.com

🛠️ 自动化测试:
运行Windows PowerShell脚本: .\test_vpn_windows.ps1
