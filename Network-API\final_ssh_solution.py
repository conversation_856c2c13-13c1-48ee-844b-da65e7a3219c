#!/usr/bin/env python3
"""
SSH连接问题的最终彻底解决方案
通过重新部署实例并确保SSH配置完全正确
"""

import boto3
import json
import base64
import time
import subprocess
import os

def main():
    print("=== SSH连接问题最终彻底解决方案 ===")
    
    # 加载配置
    with open('vpn_deployment_info.json', 'r') as f:
        info = json.load(f)
    
    ec2 = boto3.client('ec2', region_name=info['region'])
    
    # 1. 确保密钥文件正确
    print("\n1. 验证密钥文件...")
    key_file = 'Google-VPN-Key.pem'
    
    if not os.path.exists(key_file):
        print("❌ 密钥文件不存在")
        return False
    
    # 设置正确权限
    os.chmod(key_file, 0o400)
    
    # 获取公钥
    try:
        result = subprocess.run(['ssh-keygen', '-y', '-f', key_file], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            public_key = result.stdout.strip()
            print(f"✅ 公钥提取成功: {public_key[:50]}...")
        else:
            print("❌ 无法提取公钥")
            return False
    except Exception as e:
        print(f"❌ 提取公钥失败: {e}")
        return False
    
    # 2. 创建完全可靠的用户数据脚本
    print("\n2. 创建SSH修复脚本...")
    
    user_data_script = f'''#!/bin/bash
exec > /var/log/ssh-final-fix.log 2>&1
echo "=== 开始SSH最终修复 ==="
date

# 等待系统完全启动
sleep 30

# 确保基础软件包
yum update -y
yum install -y curl wget

# 确保ec2-user用户存在并配置正确
echo "配置ec2-user用户..."
if ! id ec2-user &>/dev/null; then
    useradd -m -s /bin/bash ec2-user
    echo "创建了ec2-user用户"
fi

# 配置sudo权限
echo "ec2-user ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/90-ec2-user
chmod 440 /etc/sudoers.d/90-ec2-user

# 创建.ssh目录并设置权限
echo "配置SSH目录和权限..."
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# 清空并重新配置authorized_keys
echo "配置authorized_keys文件..."
> /home/<USER>/.ssh/authorized_keys

# 方法1: 从实例元数据获取公钥
echo "从实例元数据获取公钥..."
META_KEY=$(curl -s http://***************/latest/meta-data/public-keys/0/openssh-key 2>/dev/null)
if [ ! -z "$META_KEY" ]; then
    echo "$META_KEY" >> /home/<USER>/.ssh/authorized_keys
    echo "从元数据添加了公钥"
fi

# 方法2: 直接添加匹配的公钥
echo "添加匹配的公钥..."
echo "{public_key}" >> /home/<USER>/.ssh/authorized_keys

# 去重并设置正确权限
sort /home/<USER>/.ssh/authorized_keys | uniq > /tmp/authorized_keys_clean
mv /tmp/authorized_keys_clean /home/<USER>/.ssh/authorized_keys
chmod 600 /home/<USER>/.ssh/authorized_keys
chown -R ec2-user:ec2-user /home/<USER>/.ssh

# 确保SSH服务配置正确
echo "配置SSH服务..."
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

# 创建标准SSH配置
cat > /etc/ssh/sshd_config << 'SSH_CONFIG_EOF'
Port 22
Protocol 2
PermitRootLogin no
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
PasswordAuthentication no
ChallengeResponseAuthentication no
UsePAM yes
ClientAliveInterval 60
ClientAliveCountMax 10
TCPKeepAlive yes
MaxAuthTries 10
MaxSessions 20
MaxStartups 20:30:60
LoginGraceTime 300
UseDNS no
AllowUsers ec2-user
SSH_CONFIG_EOF

# 重启SSH服务
echo "重启SSH服务..."
systemctl restart sshd
systemctl enable sshd

# 验证配置
echo "验证SSH配置..."
echo "authorized_keys文件内容:"
cat /home/<USER>/.ssh/authorized_keys
echo ""
echo "authorized_keys文件权限:"
ls -la /home/<USER>/.ssh/authorized_keys
echo ""
echo ".ssh目录权限:"
ls -la /home/<USER>/.ssh/
echo ""
echo "SSH服务状态:"
systemctl status sshd --no-pager

echo "=== SSH最终修复完成 ==="
date
'''
    
    # 3. 应用修复脚本到当前实例
    print("\n3. 应用SSH修复脚本...")
    
    encoded_script = base64.b64encode(user_data_script.encode()).decode()
    
    try:
        instance_id = info['instance_id']
        
        # 停止实例
        print("🔄 停止实例...")
        ec2.stop_instances(InstanceIds=[instance_id])
        
        # 等待停止
        waiter = ec2.get_waiter('instance_stopped')
        waiter.wait(InstanceIds=[instance_id])
        print("✅ 实例已停止")
        
        # 应用用户数据脚本
        ec2.modify_instance_attribute(
            InstanceId=instance_id,
            UserData={'Value': encoded_script}
        )
        print("✅ SSH修复脚本已应用")
        
        # 启动实例
        print("🔄 启动实例...")
        ec2.start_instances(InstanceIds=[instance_id])
        
        # 等待运行
        waiter = ec2.get_waiter('instance_running')
        waiter.wait(InstanceIds=[instance_id])
        print("✅ 实例已启动")
        
        # 等待SSH服务完全启动和脚本执行
        print("⏳ 等待SSH服务和修复脚本完全执行（180秒）...")
        time.sleep(180)
        
        # 4. 测试SSH连接
        print("\n4. 测试SSH连接...")
        
        public_ip = info['public_ip']
        ssh_cmd = [
            'ssh', '-i', key_file,
            '-o', 'StrictHostKeyChecking=no',
            '-o', 'ConnectTimeout=15',
            '-o', 'ServerAliveInterval=60',
            f'ec2-user@{public_ip}',
            'echo "SSH连接成功！" && date && whoami && uptime'
        ]
        
        result = subprocess.run(ssh_cmd, capture_output=True, text=True, timeout=20)
        
        if result.returncode == 0:
            print("🎉 SSH连接修复成功！")
            print("输出:")
            print(result.stdout)
            
            # 测试VPN相关功能
            print("\n5. 配置VPN服务...")
            vpn_cmd = [
                'ssh', '-i', key_file,
                '-o', 'StrictHostKeyChecking=no',
                f'ec2-user@{public_ip}',
                'sudo yum install -y strongswan xl2tpd ppp && echo "VPN软件包安装完成"'
            ]
            
            vpn_result = subprocess.run(vpn_cmd, capture_output=True, text=True, timeout=60)
            if vpn_result.returncode == 0:
                print("✅ VPN软件包安装成功")
            else:
                print("⚠️ VPN软件包安装可能有问题")
            
            print("\n🎉 SSH连接问题已彻底解决！")
            print("✅ VPN客户端现在可以正常通过SSH端口访问服务器")
            print(f"✅ SSH连接命令: ssh -i {key_file} ec2-user@{public_ip}")
            
            return True
            
        else:
            print("❌ SSH连接仍然失败")
            print(f"错误: {result.stderr}")
            
            # 显示详细调试信息
            print("\n=== SSH详细调试信息 ===")
            debug_cmd = [
                'ssh', '-vvv', '-i', key_file,
                '-o', 'StrictHostKeyChecking=no',
                '-o', 'ConnectTimeout=10',
                f'ec2-user@{public_ip}',
                'echo test'
            ]
            
            debug_result = subprocess.run(debug_cmd, capture_output=True, text=True, timeout=15)
            print("调试输出（最后1000字符）:")
            print(debug_result.stderr[-1000:])
            
            return False
            
    except Exception as e:
        print(f"❌ 修复过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "="*50)
        print("🎉 SSH连接问题已彻底解决！")
        print("✅ 根本原因：authorized_keys文件配置不正确")
        print("✅ 解决方案：重新配置SSH密钥和服务")
        print("✅ 结果：VPN客户端可以正常通过SSH端口访问")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("❌ SSH连接问题仍需进一步诊断")
        print("建议：检查网络配置、安全组设置或考虑重新部署")
        print("="*50)
