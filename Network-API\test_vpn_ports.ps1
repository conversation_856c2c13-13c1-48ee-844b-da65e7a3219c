# Simple VPN Port Test
param(
    [string]$ServerIP = "*************"
)

Write-Host "=== VPN Port Connectivity Test ===" -ForegroundColor Green
Write-Host "Server: $ServerIP" -ForegroundColor Cyan

# Test port connectivity
Write-Host "`nTesting VPN port connectivity..." -ForegroundColor Yellow

$ports = @(22, 500, 4500, 1701)
foreach ($port in $ports) {
    Write-Host "Testing port $port..." -NoNewline
    $result = Test-NetConnection -ComputerName $ServerIP -Port $port -WarningAction SilentlyContinue
    if ($result.TcpTestSucceeded) {
        Write-Host " REACHABLE" -ForegroundColor Green
    } else {
        Write-Host " NOT REACHABLE" -ForegroundColor Red
    }
}

# Get current IP
Write-Host "`nGetting current public IP..." -ForegroundColor Yellow
try {
    $currentIP = (Invoke-WebRequest -Uri "https://api.ipify.org" -UseBasicParsing -TimeoutSec 10).Content
    Write-Host "Current IP: $currentIP" -ForegroundColor Cyan
} catch {
    Write-Host "Cannot get current IP" -ForegroundColor Red
}

# Display VPN configuration
Write-Host "`n=== VPN Configuration ===" -ForegroundColor Green
Write-Host "Server Address: $ServerIP" -ForegroundColor Cyan
Write-Host "VPN Type: IKEv2" -ForegroundColor Cyan
Write-Host "Username: vpnuser" -ForegroundColor Cyan
Write-Host "Password: GoogleVPN2023!" -ForegroundColor Cyan
Write-Host "Pre-shared Key: GoogleVPN2023!" -ForegroundColor Cyan

Write-Host "`n=== Manual Setup Steps ===" -ForegroundColor Green
Write-Host "1. Open Settings -> Network & Internet -> VPN" -ForegroundColor White
Write-Host "2. Click Add a VPN connection" -ForegroundColor White
Write-Host "3. Choose Windows (built-in)" -ForegroundColor White
Write-Host "4. Connection name: TestVPN" -ForegroundColor White
Write-Host "5. Server name or address: $ServerIP" -ForegroundColor White
Write-Host "6. VPN type: IKEv2" -ForegroundColor White
Write-Host "7. Type of sign-in info: User name and password" -ForegroundColor White
Write-Host "8. User name: vpnuser" -ForegroundColor White
Write-Host "9. Password: GoogleVPN2023!" -ForegroundColor White
Write-Host "10. Click Save and Connect" -ForegroundColor White

Write-Host "`nTest completed!" -ForegroundColor Green
