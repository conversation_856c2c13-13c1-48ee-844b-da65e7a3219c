#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AWS VPN资源管理器 (现代化改造版)
功能：
- 动态读取当前部署状态
- 检查和修复EC2实例的IAM角色
- 为IAM用户授予SSM权限
- 智能资源复用和状态检查
"""

import boto3
import json
import sys
import os
from botocore.exceptions import ClientError

# --- 辅助函数 ---
def print_info(message):
    print(f"ℹ️  {message}")

def print_success(message):
    print(f"✅ {message}")

def print_warning(message):
    print(f"⚠️  {message}")

def print_error(message):
    print(f"❌ {message}")

# --- 核心功能 ---

def load_config():
    """动态加载部署信息和配置"""
    print_info("加载配置文件...")
    try:
        if not os.path.exists('vpn_deployment_info.json'):
            print_error("未找到 'vpn_deployment_info.json'。请先运行部署脚本。")
            return None
        with open('vpn_deployment_info.json', 'r') as f:
            config = json.load(f)
        print_success("成功加载 'vpn_deployment_info.json'")
        return config
    except Exception as e:
        print_error(f"加载配置失败: {e}")
        return None

def check_instance_status(ec2, instance_id):
    """检查当前EC2实例的状态"""
    print_info(f"1. 检查实例 {instance_id} 状态...")
    try:
        response = ec2.describe_instances(InstanceIds=[instance_id])
        if not response['Reservations']:
            print_error(f"实例 {instance_id} 不存在。")
            return None
        instance = response['Reservations'][0]['Instances'][0]
        
        print(f"   - 实例ID: {instance['InstanceId']}")
        print(f"   - 状态: {instance['State']['Name']}")
        print(f"   - 公网IP: {instance.get('PublicIpAddress', 'N/A')}")
        
        iam_profile = instance.get('IamInstanceProfile', {}).get('Arn')
        if iam_profile:
            print(f"   - IAM角色: {iam_profile.split('/')[-1]}")
            print_success("实例已关联IAM角色，可以使用Session Manager。")
        else:
            print_warning("实例缺少IAM角色，SSM功能可能受限。")
        return instance
    except ClientError as e:
        print_error(f"检查实例失败: {e.response['Error']['Message']}")
        return None

def ensure_ec2_iam_role(iam):
    """确保用于EC2的SSM IAM角色存在"""
    role_name = 'EC2-SSM-Role'
    policy_arn = 'arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore'
    
    print_info(f"2. 检查EC2的IAM角色 '{role_name}'...")
    try:
        iam.get_role(RoleName=role_name)
        print_success(f"角色 '{role_name}' 已存在。")
        return role_name
    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchEntity':
            print_warning(f"角色 '{role_name}' 不存在，正在创建...")
            try:
                trust_policy = {
                    "Version": "2012-10-17",
                    "Statement": [{
                        "Effect": "Allow",
                        "Principal": {"Service": "ec2.amazonaws.com"},
                        "Action": "sts:AssumeRole"
                    }]
                }
                iam.create_role(
                    RoleName=role_name,
                    AssumeRolePolicyDocument=json.dumps(trust_policy),
                    Description='Allows EC2 instances to call AWS services on your behalf.'
                )
                iam.attach_role_policy(RoleName=role_name, PolicyArn=policy_arn)
                
                # 创建实例配置文件
                try:
                    iam.create_instance_profile(InstanceProfileName=role_name)
                except iam.exceptions.EntityAlreadyExistsException:
                    pass # 如果已存在，忽略
                
                iam.add_role_to_instance_profile(InstanceProfileName=role_name, RoleName=role_name)
                
                print_success(f"角色 '{role_name}' 创建并配置成功。")
                return role_name
            except Exception as create_e:
                print_error(f"创建角色失败: {create_e}")
                return None
        else:
            print_error(f"检查角色失败: {e}")
            return None

def grant_user_ssm_access(iam, user_name):
    """为指定IAM用户授予SSM完全访问权限"""
    policy_arn = 'arn:aws:iam::aws:policy/AmazonSSMFullAccess'
    print_info(f"3. 为用户 '{user_name}' 授予SSM权限...")
    
    try:
        # 检查用户是否存在
        iam.get_user(UserName=user_name)
        print(f"   - 用户 '{user_name}' 存在。")
        
        # 附加策略
        iam.attach_user_policy(UserName=user_name, PolicyArn=policy_arn)
        print_success(f"成功将策略 '{policy_arn.split('/')[-1]}' 附加到用户 '{user_name}'。")
        print_info(f"用户 '{user_name}' 现在应该有权限执行 ssm:SendCommand。")
        return True
    except iam.exceptions.NoSuchEntityException:
        print_error(f"用户 '{user_name}' 不存在。请检查用户名。")
        return False
    except ClientError as e:
        # 如果策略已附加，Boto3不会报错，所以无需检查重复
        print_error(f"附加策略失败: {e.response['Error']['Message']}")
        return False

def display_connection_info(config):
    """显示最终的连接信息"""
    print_info("4. VPN连接信息:")
    print(f"   - 服务器地址: {config.get('public_ip', 'N/A')}")
    print(f"   - 预共享密钥: (请查看 vpn_config.ini)")
    print(f"   - 用户名: vpnuser")
    print_info("SSM Session Manager 连接命令:")
    print(f"   aws ssm start-session --target {config.get('instance_id', 'N/A')} --region {config.get('region', 'N/A')}")

def main():
    """主程序，支持交互式菜单和命令行参数"""
    # 命令行模式
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == 'grant_ssm_access' and len(sys.argv) > 2:
            user_to_grant = sys.argv[2]
            print_info(f"命令行模式: 为用户 '{user_to_grant}' 授予SSM权限...")
            config = load_config()
            if not config: sys.exit(1)
            iam = boto3.client('iam')
            if grant_user_ssm_access(iam, user_to_grant):
                print_success("权限授予操作完成。现在可以尝试重新运行 'fix_server_config.py'。")
            else:
                print_error("权限授予失败，请检查上面的错误日志。")
            sys.exit(0)
        else:
            print_error(f"无效的命令行参数。用法: python {sys.argv[0]} grant_ssm_access <IAM_USER_NAME>")
            sys.exit(1)

    # 交互式菜单模式
    while True:
        print("\n" + "="*50)
        print("🚀 AWS VPN 现代化管理工具")
        print("="*50)
        print("1. 检查当前部署状态")
        print("2. 为用户授予SSM权限 (修复AccessDenied错误)")
        print("3. 退出")
        print("="*50)
        
        choice = input("请选择操作 (1-3): ").strip()

        if choice == '1':
            config = load_config()
            if not config: continue
            ec2 = boto3.client('ec2', region_name=config.get('region'))
            check_instance_status(ec2, config.get('instance_id'))
            display_connection_info(config)

        elif choice == '2':
            config = load_config()
            if not config: continue
            
            user_to_grant = input("请输入需要授予权限的IAM用户名 (例如 EastStar01): ").strip()
            if not user_to_grant:
                print_error("用户名不能为空。")
                continue
            
            iam = boto3.client('iam')
            if grant_user_ssm_access(iam, user_to_grant):
                print_success("权限授予操作完成。现在可以尝试重新运行 'fix_server_config.py'。")
            else:
                print_error("权限授予失败，请检查上面的错误日志。")

        elif choice == '3':
            print_info("👋 再见！")
            break
        else:
            print_error("无效选择，请输入1-3。")
        
        input("\n按回车键继续...")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n操作已取消。👋")
        sys.exit(0)
    except Exception as e:
        print_error(f"发生未知错误: {e}")
        sys.exit(1)
