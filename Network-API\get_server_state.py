import boto3
import json
from botocore.exceptions import ClientError
import logging
import sys
import os

# --- Configuration ---
LOGGING_LEVEL = logging.INFO
DEPLOYMENT_INFO_FILE = 'vpn_deployment_info.json'

# --- Setup Logging ---
logging.basicConfig(level=LOGGING_LEVEL, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_firewall_state(ssm_client, instance_id):
    """Gets the current firewalld status and rules from the instance via SSM."""
    logger.info("Fetching current firewalld state from instance...")
    try:
        # Check firewalld status and rules in one go
        command = """
        echo "--- firewalld status ---"
        systemctl status firewalld
        echo "\n--- firewalld rules ---"
        firewall-cmd --list-all
        """
        response = ssm_client.send_command(
            InstanceIds=[instance_id],
            DocumentName='AWS-RunShellScript',
            Parameters={'commands': [command]},
            TimeoutSeconds=60,
            Comment='Fetch current firewalld state'
        )
        command_id = response['Command']['CommandId']
        
        waiter = ssm_client.get_waiter('command_executed')
        waiter.wait(CommandId=command_id, InstanceId=instance_id, WaiterConfig={'Delay': 5, 'MaxAttempts': 12})
        
        output = ssm_client.get_command_invocation(CommandId=command_id, InstanceId=instance_id)
        if output['Status'] == 'Success':
            logger.info("Successfully fetched firewalld state.")
            return output.get('StandardOutputContent', '# No firewalld info found')
        else:
            logger.error(f"Failed to fetch firewalld state. Status: {output['Status']}")
            logger.error(output.get('StandardErrorContent', ''))
            return None
    except Exception as e:
        logger.error(f"An error occurred while fetching firewalld state: {e}")
        return None

def get_network_acls(ec2_client, subnet_id):
    """Gets the Network ACLs associated with the instance's subnet."""
    logger.info(f"Fetching Network ACLs for subnet '{subnet_id}'...")
    try:
        response = ec2_client.describe_network_acls(
            Filters=[{'Name': 'association.subnet-id', 'Values': [subnet_id]}]
        )
        if not response.get('NetworkAcls'):
            logger.warning(f"No specific Network ACL found for subnet '{subnet_id}'. It might be using the default ACL.")
            # Now, let's get the default NACL for the VPC
            subnet_response = ec2_client.describe_subnets(SubnetIds=[subnet_id])
            vpc_id = subnet_response['Subnets'][0]['VpcId']
            response = ec2_client.describe_network_acls(
                Filters=[{'Name': 'default', 'Values': ['true']}, {'Name': 'vpc-id', 'Values': [vpc_id]}]
            )

        return response.get('NetworkAcls', [])
    except Exception as e:
        logger.error(f"An error occurred while fetching Network ACLs: {e}")
        return None

def analyze_nacl_rules(nacl):
    """Analyzes NACL rules and prints them in a readable format."""
    print(f"\n--- Analyzing Network ACL: {nacl['NetworkAclId']} ---")
    
    print("\n  Inbound Rules:")
    inbound_ssh_allowed = False
    for entry in sorted(nacl['Entries'], key=lambda x: x['RuleNumber']):
        if entry['Egress']: continue # Skip egress rules
        
        action = entry['RuleAction'].upper()
        protocol = entry.get('Protocol', 'ALL') # -1 is ALL
        port_range = entry.get('PortRange', {'From': 'ALL', 'To': ''})
        ports = f"{port_range['From']}" if port_range['To'] == '' else f"{port_range['From']}-{port_range['To']}"
        cidr = entry['CidrBlock']
        
        print(f"    Rule #{entry['RuleNumber']}: {action} | Protocol: {protocol} | Ports: {ports} | Source: {cidr}")

        # Check for SSH allow rule
        if action == 'ALLOW' and (protocol == '6' or protocol == '-1'): # TCP or ALL
            from_port = port_range.get('From')
            to_port = port_range.get('To')
            # Check if from_port and to_port are integers before comparing
            if isinstance(from_port, int) and isinstance(to_port, int):
                if from_port <= 22 <= to_port:
                    inbound_ssh_allowed = True
            # If there is no port range, it means all ports for that protocol
            elif 'From' not in port_range:
                inbound_ssh_allowed = True
            # Handle the case where the rule is for all ports explicitly
            elif from_port is None and to_port is None:
                 inbound_ssh_allowed = True

    print("\n  Egress Rules:")
    for entry in sorted(nacl['Entries'], key=lambda x: x['RuleNumber']):
        if not entry['Egress']: continue # Skip ingress rules
        action = entry['RuleAction'].upper()
        print(f"    Rule #{entry['RuleNumber']}: {action} ...") # Keep it brief

    print("\n  SSH Status:")
    if inbound_ssh_allowed:
        logger.info("✅ NACL rules appear to ALLOW inbound SSH traffic (TCP/22).")
    else:
        logger.warning("⚠️ NACL rules DO NOT appear to explicitly ALLOW inbound SSH traffic. This could be the problem.")


def main():
    """Main function."""
    logger.info("--- Server State Checker (iptables & NACLs) ---")
    
    # Load deployment info
    if not os.path.exists(DEPLOYMENT_INFO_FILE):
        logger.error(f"Deployment info file '{DEPLOYMENT_INFO_FILE}' not found.")
        sys.exit(1)
    with open(DEPLOYMENT_INFO_FILE, 'r') as f:
        deployment_info = json.load(f)
    
    instance_id = deployment_info.get('instance_id')
    region = deployment_info.get('region')
    subnet_id = deployment_info.get('subnet_id')
    
    if not all([instance_id, region, subnet_id]):
        logger.error("Instance ID, region, or subnet ID not found in deployment info file.")
        sys.exit(1)
        
    ssm_client = boto3.client('ssm', region_name=region)
    ec2_client = boto3.client('ec2', region_name=region)

    # 1. Get firewalld state
    firewall_state = get_firewall_state(ssm_client, instance_id)
    if firewall_state:
        print("\n--- FIREWALLD STATE ON INSTANCE ---")
        print(firewall_state)
        print("-----------------------------------")

    # 2. Get and analyze Network ACLs
    nacls = get_network_acls(ec2_client, subnet_id)
    if nacls:
        for nacl in nacls:
            analyze_nacl_rules(nacl)
    else:
        logger.error("Could not retrieve Network ACL information.")

if __name__ == "__main__":
    main()
