# VPN客户端配置指南

## 服务器信息
- 服务器地址: *************
- 预共享密钥: GoogleVPN2023!
- 用户名: vpnuser (或 testuser, admin)
- 密码: GoogleVPN2023!

## Windows 连接方法

### 方法1: IKEv2 (推荐)
1. 设置 → 网络和Internet → VPN → 添加VPN连接
2. 配置参数:
   - VPN提供商: Windows (内置)
   - 连接名称: Google-VPN
   - 服务器: *************
   - VPN类型: IKEv2
   - 登录信息类型: 用户名和密码
   - 用户名: vpnuser
   - 密码: GoogleVPN2023!

### 方法2: L2TP/IPSec (备用)
1. 设置 → 网络和Internet → VPN → 添加VPN连接
2. 配置参数:
   - VPN提供商: Windows (内置)
   - 连接名称: Google-VPN-L2TP
   - 服务器: *************
   - VPN类型: L2TP/IPSec (预共享密钥)
   - 预共享密钥: GoogleVPN2023!
   - 用户名: vpnuser
   - 密码: GoogleVPN2023!

## 移动设备连接

### iOS
1. 设置 → 通用 → VPN → 添加VPN配置
2. 类型: IKEv2
3. 服务器: *************
4. 远程ID: *************
5. 用户名: vpnuser
6. 密码: GoogleVPN2023!

### Android
1. 设置 → 网络和互联网 → VPN → 添加VPN
2. 类型: IKEv2/IPSec PSK
3. 服务器地址: *************
4. IPSec标识符: *************
5. 预共享密钥: GoogleVPN2023!

## 连接验证
连接成功后访问: https://whatismyipaddress.com
应显示IP: ************* (美国弗吉尼亚州)
