#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查系统信息和可用软件包
"""

import boto3
import json
import time

def send_command(ssm, instance_id, commands, description):
    """发送单个命令"""
    try:
        print(f"\n🔧 {description}")
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={'commands': commands},
            TimeoutSeconds=120
        )
        
        command_id = response['Command']['CommandId']
        
        # 等待命令完成
        for i in range(30):
            time.sleep(4)
            try:
                result = ssm.get_command_invocation(
                    CommandId=command_id,
                    InstanceId=instance_id
                )
                
                status = result['Status']
                if status == 'Success':
                    output = result['StandardOutputContent']
                    if output.strip():
                        print(f"✅ 输出:\n{output}")
                    else:
                        print("✅ 成功")
                    return True
                elif status == 'Failed':
                    error = result['StandardErrorContent']
                    print(f"❌ 失败: {error}")
                    return False
                elif status in ['InProgress', 'Pending']:
                    continue
                    
            except Exception as e:
                if 'InvocationDoesNotExist' in str(e):
                    continue
                else:
                    print(f"❌ 错误: {e}")
                    return False
        
        print("⚠️ 超时")
        return False
        
    except Exception as e:
        print(f"❌ 发送命令失败: {e}")
        return False

def main():
    try:
        # 读取部署信息
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        instance_id = deployment_info['instance_id']
        region = deployment_info['region']
        
        print(f"实例ID: {instance_id}")
        
        # 创建SSM客户端
        ssm = boto3.client('ssm', region_name=region)
        
        # 检查系统信息
        send_command(ssm, instance_id, [
            'cat /etc/os-release',
            'uname -a'
        ], "检查系统版本")
        
        # 检查网络接口
        send_command(ssm, instance_id, [
            'ip addr show',
            'ls /proc/sys/net/ipv4/conf/'
        ], "检查网络接口")
        
        # 检查可用的VPN软件包
        send_command(ssm, instance_id, [
            'dnf search libreswan',
            'dnf search strongswan',
            'dnf search openvpn',
            'dnf search xl2tpd'
        ], "搜索VPN软件包")
        
        # 检查防火墙工具
        send_command(ssm, instance_id, [
            'which iptables',
            'which firewall-cmd',
            'dnf search firewalld',
            'dnf search iptables'
        ], "检查防火墙工具")
        
        # 检查当前安装的软件包
        send_command(ssm, instance_id, [
            'rpm -qa | grep -i ipsec',
            'rpm -qa | grep -i vpn',
            'systemctl list-unit-files | grep -i ipsec'
        ], "检查已安装的VPN相关软件")
        
        print("\n✅ 系统信息检查完成！")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    print("=== 系统信息检查工具 ===")
    main()
