#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的SSM命令执行工具
"""

import boto3
import json
import time

def run_simple_command():
    """运行简单的SSM命令"""
    try:
        # 读取部署信息
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        instance_id = deployment_info['instance_id']
        region = deployment_info['region']
        
        print(f"实例ID: {instance_id}")
        print(f"地区: {region}")
        
        # 创建SSM客户端
        ssm = boto3.client('ssm', region_name=region)
        
        # 发送简单命令
        print("\n发送命令: 检查系统状态...")
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={
                'commands': [
                    'echo "=== 系统信息 ==="',
                    'whoami',
                    'date',
                    'uptime',
                    'echo "=== SSH服务状态 ==="',
                    'systemctl is-active sshd || echo "sshd not active"',
                    'systemctl is-enabled sshd || echo "sshd not enabled"',
                    'echo "=== VPN服务状态 ==="',
                    'systemctl is-active ipsec || echo "ipsec not active"',
                    'systemctl is-active xl2tpd || echo "xl2tpd not active"',
                    'echo "=== 监听端口 ==="',
                    'ss -tlnp | grep -E ":(22|500|1701|4500)" || echo "未找到相关端口"',
                    'echo "=== 公网IP ==="',
                    'curl -s http://***************/latest/meta-data/public-ipv4 || echo "无法获取公网IP"'
                ]
            }
        )
        
        command_id = response['Command']['CommandId']
        print(f"命令ID: {command_id}")
        
        # 等待命令完成
        print("等待命令执行...")
        for i in range(15):  # 等待最多15秒
            time.sleep(1)
            try:
                result = ssm.get_command_invocation(
                    CommandId=command_id,
                    InstanceId=instance_id
                )
                
                status = result['Status']
                print(f"状态: {status}")
                
                if status == 'Success':
                    print("\n✅ 命令执行成功！")
                    print("=== 输出结果 ===")
                    print(result['StandardOutputContent'])
                    if result['StandardErrorContent']:
                        print("=== 错误输出 ===")
                        print(result['StandardErrorContent'])
                    return True
                elif status == 'Failed':
                    print("\n❌ 命令执行失败！")
                    print("=== 错误输出 ===")
                    print(result['StandardErrorContent'])
                    return False
                elif status in ['InProgress', 'Pending']:
                    continue
                else:
                    print(f"未知状态: {status}")
                    return False
                    
            except Exception as e:
                if 'InvocationDoesNotExist' in str(e):
                    continue
                else:
                    print(f"获取命令结果失败: {e}")
                    return False
        
        print("⚠️ 命令执行超时")
        return False
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

if __name__ == "__main__":
    print("=== 简单SSM状态检查 ===")
    run_simple_command()
