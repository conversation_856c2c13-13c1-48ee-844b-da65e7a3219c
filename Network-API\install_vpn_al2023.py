#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Linux 2023 VPN安装脚本
"""

import boto3
import json
import time

def send_command(ssm, instance_id, commands, description):
    """发送单个命令"""
    try:
        print(f"\n🔧 {description}")
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={'commands': commands},
            TimeoutSeconds=180
        )
        
        command_id = response['Command']['CommandId']
        
        # 等待命令完成
        for i in range(45):  # 等待最多3分钟
            time.sleep(4)
            try:
                result = ssm.get_command_invocation(
                    CommandId=command_id,
                    InstanceId=instance_id
                )
                
                status = result['Status']
                if status == 'Success':
                    output = result['StandardOutputContent']
                    if output.strip():
                        print(f"✅ 成功: {output[-800:]}")  # 显示最后800字符
                    else:
                        print("✅ 成功")
                    return True
                elif status == 'Failed':
                    error = result['StandardErrorContent']
                    print(f"❌ 失败: {error}")
                    return False
                elif status in ['InProgress', 'Pending']:
                    continue
                    
            except Exception as e:
                if 'InvocationDoesNotExist' in str(e):
                    continue
                else:
                    print(f"❌ 错误: {e}")
                    return False
        
        print("⚠️ 超时")
        return False
        
    except Exception as e:
        print(f"❌ 发送命令失败: {e}")
        return False

def main():
    try:
        # 读取部署信息
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        instance_id = deployment_info['instance_id']
        region = deployment_info['region']
        
        print(f"实例ID: {instance_id}")
        
        # 创建SSM客户端
        ssm = boto3.client('ssm', region_name=region)
        
        # 步骤1: 系统更新和安装基础软件
        send_command(ssm, instance_id, [
            'dnf update -y',
            'dnf install -y libreswan strongswan'
        ], "更新系统并安装VPN软件")
        
        # 步骤2: 配置系统参数
        send_command(ssm, instance_id, [
            'echo "net.ipv4.ip_forward = 1" >> /etc/sysctl.conf',
            'echo "net.ipv4.conf.all.accept_redirects = 0" >> /etc/sysctl.conf',
            'echo "net.ipv4.conf.all.send_redirects = 0" >> /etc/sysctl.conf',
            'echo "net.ipv4.conf.all.rp_filter = 0" >> /etc/sysctl.conf',
            'echo "net.ipv4.conf.default.rp_filter = 0" >> /etc/sysctl.conf',
            'echo "net.ipv4.conf.eth0.rp_filter = 0" >> /etc/sysctl.conf',
            'sysctl -p'
        ], "配置系统网络参数")
        
        # 步骤3: 获取公网IP
        send_command(ssm, instance_id, [
            'TOKEN=$(curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")',
            'PUBLIC_IP=$(curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/public-ipv4)',
            'echo "公网IP: $PUBLIC_IP"',
            'echo $PUBLIC_IP > /tmp/public_ip'
        ], "获取公网IP地址")
        
        # 步骤4: 配置libreswan IPSec
        send_command(ssm, instance_id, [
            'PUBLIC_IP=$(cat /tmp/public_ip)',
            'cat > /etc/ipsec.conf << EOF',
            'version 2.0',
            'config setup',
            '    nat_traversal=yes',
            '    virtual_private=%v4:10.0.0.0/8,%v4:***********/16,%v4:**********/12',
            '    oe=off',
            '    protostack=netkey',
            '    nhelpers=0',
            '    interfaces=%defaultroute',
            '',
            'conn ikev2-vpn',
            '    auto=add',
            '    compress=no',
            '    type=tunnel',
            '    keyexchange=ikev2',
            '    fragmentation=yes',
            '    forceencaps=yes',
            '    dpdaction=clear',
            '    dpddelay=300s',
            '    rekey=no',
            '    left=%defaultroute',
            '    leftid=$PUBLIC_IP',
            '    leftauth=psk',
            '    leftsubnet=0.0.0.0/0',
            '    right=%any',
            '    rightid=%any',
            '    rightauth=psk',
            '    rightsourceip=**********/24',
            '    rightdns=*******,*******',
            'EOF'
        ], "配置libreswan IPSec")
        
        # 步骤5: 配置预共享密钥
        send_command(ssm, instance_id, [
            'PUBLIC_IP=$(cat /tmp/public_ip)',
            'cat > /etc/ipsec.secrets << EOF',
            ': PSK "GoogleVPN2023!"',
            '$PUBLIC_IP %any : PSK "GoogleVPN2023!"',
            'EOF',
            'chmod 600 /etc/ipsec.secrets'
        ], "配置预共享密钥")
        
        # 步骤6: 启动和启用IPSec服务
        send_command(ssm, instance_id, [
            'systemctl enable ipsec',
            'systemctl start ipsec',
            'systemctl status ipsec --no-pager'
        ], "启动IPSec服务")
        
        # 步骤7: 配置防火墙
        send_command(ssm, instance_id, [
            'systemctl enable firewalld',
            'systemctl start firewalld',
            'firewall-cmd --permanent --add-service=ipsec',
            'firewall-cmd --permanent --add-port=500/udp',
            'firewall-cmd --permanent --add-port=4500/udp',
            'firewall-cmd --permanent --add-masquerade',
            'firewall-cmd --reload',
            'firewall-cmd --list-all'
        ], "配置防火墙规则")
        
        # 步骤8: 配置NAT规则
        send_command(ssm, instance_id, [
            'firewall-cmd --permanent --direct --add-rule ipv4 nat POSTROUTING 0 -s **********/24 -o eth0 -j MASQUERADE',
            'firewall-cmd --permanent --direct --add-rule ipv4 filter FORWARD 0 -i eth0 -o ppp+ -j ACCEPT',
            'firewall-cmd --permanent --direct --add-rule ipv4 filter FORWARD 0 -i ppp+ -o eth0 -j ACCEPT',
            'firewall-cmd --reload'
        ], "配置NAT转发规则")
        
        # 步骤9: 检查最终状态
        send_command(ssm, instance_id, [
            'systemctl status ipsec --no-pager',
            'ipsec status',
            'ss -unp | grep -E ":(500|4500)"',
            'ipsec verify'
        ], "检查VPN服务状态")
        
        print("\n✅ Amazon Linux 2023 VPN安装完成！")
        print("支持的连接方式:")
        print("- IKEv2 + PSK (预共享密钥): GoogleVPN2023!")
        print("- 客户端IP池: **********/24")
        print("- DNS服务器: *******, *******")
        
    except Exception as e:
        print(f"❌ 安装失败: {e}")

if __name__ == "__main__":
    print("=== Amazon Linux 2023 VPN安装工具 ===")
    main()
