#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AWS资源管理器 - 智能资源复用和清理
优先复用现有资源，避免重复创建，节省成本
"""

import boto3
import json
import logging
from datetime import datetime, timedelta
from botocore.exceptions import ClientError

class AWSResourceManager:
    def __init__(self, region='us-east-1'):
        self.region = region
        self.ec2 = boto3.client('ec2', region_name=region)
        self.logger = logging.getLogger(__name__)
        
        # 资源复用策略
        self.REUSE_POLICY = {
            'max_instances': 1,  # 最多保留1个VPN实例
            'prefer_running': True,  # 优先使用运行中的实例
            'auto_cleanup': True,  # 自动清理无用资源
            'cost_optimization': True,  # 启用成本优化
            'force_reuse': True,  # 强制复用，禁止创建新资源
            'allowed_instance_types': ['t3.micro', 't3.nano'],  # 只允许最小实例类型
            'default_instance_type': 't3.micro'  # 默认实例类型
        }
    
    def find_reusable_instance(self):
        """查找可复用的VPN实例"""
        print("🔍 查找可复用的VPN实例...")
        
        try:
            # 查找所有VPN相关实例
            response = self.ec2.describe_instances(
                Filters=[
                    {'Name': 'tag:Name', 'Values': [
                        'Google-VPN-Server*', 
                        '*VPN*', 
                        '*vpn*'
                    ]},
                    {'Name': 'instance-state-name', 'Values': [
                        'running', 'stopped', 'stopping', 'starting'
                    ]}
                ]
            )
            
            instances = []
            for reservation in response['Reservations']:
                for instance in reservation['Instances']:
                    instances.append({
                        'id': instance['InstanceId'],
                        'state': instance['State']['Name'],
                        'type': instance['InstanceType'],
                        'launch_time': instance['LaunchTime'],
                        'public_ip': instance.get('PublicIpAddress'),
                        'name': self._get_instance_name(instance),
                        'key_name': instance.get('KeyName'),
                        'vpc_id': instance.get('VpcId'),
                        'subnet_id': instance.get('SubnetId'),
                        'security_groups': [sg['GroupId'] for sg in instance.get('SecurityGroups', [])]
                    })
            
            if not instances:
                print("❌ 没有找到可复用的VPN实例")
                return None
            
            # 按优先级排序：运行中 > 已停止 > 其他
            instances.sort(key=lambda x: (
                0 if x['state'] == 'running' else
                1 if x['state'] == 'stopped' else 2,
                x['launch_time']
            ), reverse=True)
            
            # 选择最佳实例
            best_instance = instances[0]
            print(f"✅ 找到可复用实例: {best_instance['id']} ({best_instance['name']})")
            print(f"   状态: {best_instance['state']}, IP: {best_instance['public_ip']}")
            
            # 标记其他实例为待删除
            if len(instances) > 1:
                print(f"⚠️ 发现 {len(instances)-1} 个重复实例，将被清理")
                for inst in instances[1:]:
                    print(f"   待删除: {inst['id']} ({inst['name']}) - {inst['state']}")
            
            return best_instance
            
        except Exception as e:
            print(f"❌ 查找实例失败: {e}")
            return None
    
    def cleanup_duplicate_instances(self, keep_instance_id):
        """清理重复的VPN实例"""
        print("🧹 清理重复的VPN实例...")
        
        try:
            response = self.ec2.describe_instances(
                Filters=[
                    {'Name': 'tag:Name', 'Values': [
                        'Google-VPN-Server*', 
                        '*VPN*', 
                        '*vpn*'
                    ]},
                    {'Name': 'instance-state-name', 'Values': [
                        'running', 'stopped', 'stopping', 'starting'
                    ]}
                ]
            )
            
            instances_to_delete = []
            for reservation in response['Reservations']:
                for instance in reservation['Instances']:
                    instance_id = instance['InstanceId']
                    if instance_id != keep_instance_id:
                        instances_to_delete.append({
                            'id': instance_id,
                            'name': self._get_instance_name(instance),
                            'state': instance['State']['Name']
                        })
            
            if instances_to_delete:
                print(f"删除 {len(instances_to_delete)} 个重复实例:")
                instance_ids = [inst['id'] for inst in instances_to_delete]
                
                for inst in instances_to_delete:
                    print(f"  ❌ {inst['id']} ({inst['name']}) - {inst['state']}")
                
                # 终止实例
                self.ec2.terminate_instances(InstanceIds=instance_ids)
                print("✅ 重复实例已标记为终止")
                
                return len(instances_to_delete)
            else:
                print("✅ 没有重复实例需要清理")
                return 0
                
        except Exception as e:
            print(f"❌ 清理重复实例失败: {e}")
            return 0
    
    def cleanup_unused_resources(self):
        """清理未使用的AWS资源"""
        print("🧹 清理未使用的AWS资源...")
        
        cleanup_results = {
            'eips': 0,
            'volumes': 0,
            'snapshots': 0,
            'security_groups': 0,
            'network_interfaces': 0
        }
        
        # 1. 清理未关联的弹性IP
        try:
            eips = self.ec2.describe_addresses()
            unattached_eips = [
                eip for eip in eips['Addresses'] 
                if 'InstanceId' not in eip
            ]
            
            if unattached_eips:
                print(f"发现 {len(unattached_eips)} 个未关联弹性IP:")
                for eip in unattached_eips:
                    print(f"  释放: {eip['PublicIp']} ({eip['AllocationId']})")
                    self.ec2.release_address(AllocationId=eip['AllocationId'])
                    cleanup_results['eips'] += 1
                print("✅ 未关联弹性IP已释放")
            else:
                print("✅ 没有未关联的弹性IP")
                
        except Exception as e:
            print(f"❌ 清理弹性IP失败: {e}")
        
        # 2. 清理未使用的EBS卷
        try:
            volumes = self.ec2.describe_volumes()
            available_volumes = [
                vol for vol in volumes['Volumes'] 
                if vol['State'] == 'available'
            ]
            
            if available_volumes:
                print(f"发现 {len(available_volumes)} 个未使用EBS卷:")
                for vol in available_volumes:
                    print(f"  删除: {vol['VolumeId']} ({vol['Size']}GB)")
                    self.ec2.delete_volume(VolumeId=vol['VolumeId'])
                    cleanup_results['volumes'] += 1
                print("✅ 未使用EBS卷已删除")
            else:
                print("✅ 没有未使用的EBS卷")
                
        except Exception as e:
            print(f"❌ 清理EBS卷失败: {e}")
        
        # 3. 清理旧快照（超过30天）
        try:
            snapshots = self.ec2.describe_snapshots(OwnerIds=['self'])
            old_snapshots = []
            
            cutoff_date = datetime.now(datetime.now().astimezone().tzinfo) - timedelta(days=30)
            
            for snapshot in snapshots['Snapshots']:
                if snapshot['StartTime'] < cutoff_date:
                    old_snapshots.append(snapshot)
            
            if old_snapshots:
                print(f"发现 {len(old_snapshots)} 个旧快照 (>30天):")
                for snap in old_snapshots:
                    age = (datetime.now(snap['StartTime'].tzinfo) - snap['StartTime']).days
                    print(f"  删除: {snap['SnapshotId']} ({age}天前)")
                    self.ec2.delete_snapshot(SnapshotId=snap['SnapshotId'])
                    cleanup_results['snapshots'] += 1
                print("✅ 旧快照已删除")
            else:
                print("✅ 没有需要清理的旧快照")
                
        except Exception as e:
            print(f"❌ 清理快照失败: {e}")
        
        return cleanup_results
    
    def optimize_instance_usage(self, instance_id):
        """优化实例使用"""
        print(f"⚡ 优化实例使用: {instance_id}")
        
        try:
            # 检查实例状态
            response = self.ec2.describe_instances(InstanceIds=[instance_id])
            instance = response['Reservations'][0]['Instances'][0]
            
            state = instance['State']['Name']
            instance_type = instance['InstanceType']
            
            print(f"当前状态: {state}, 类型: {instance_type}")
            
            # 如果实例已停止，启动它
            if state == 'stopped':
                print("启动已停止的实例...")
                self.ec2.start_instances(InstanceIds=[instance_id])
                
                # 等待实例运行
                waiter = self.ec2.get_waiter('instance_running')
                waiter.wait(InstanceIds=[instance_id])
                print("✅ 实例已启动")
            
            # 确保有弹性IP
            addresses = self.ec2.describe_addresses(
                Filters=[{'Name': 'instance-id', 'Values': [instance_id]}]
            )
            
            if not addresses['Addresses']:
                print("分配弹性IP...")
                allocation = self.ec2.allocate_address(Domain='vpc')
                self.ec2.associate_address(
                    InstanceId=instance_id,
                    AllocationId=allocation['AllocationId']
                )
                print(f"✅ 弹性IP已分配: {allocation['PublicIp']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 优化实例失败: {e}")
            return False
    
    def _get_instance_name(self, instance):
        """获取实例名称"""
        for tag in instance.get('Tags', []):
            if tag['Key'] == 'Name':
                return tag['Value']
        return '未命名'

    def validate_resource_creation(self, resource_type, **kwargs):
        """验证资源创建是否符合复用策略"""
        if not self.REUSE_POLICY['force_reuse']:
            return True

        if resource_type == 'instance':
            # 检查是否已有可复用实例
            reusable = self.find_reusable_instance()
            if reusable:
                raise Exception(f"❌ 资源复用策略：发现可复用实例 {reusable['id']}，禁止创建新实例！")

            # 检查实例类型是否被允许
            instance_type = kwargs.get('instance_type', self.REUSE_POLICY['default_instance_type'])
            if instance_type not in self.REUSE_POLICY['allowed_instance_types']:
                raise Exception(f"❌ 实例类型 {instance_type} 不被允许，只能使用: {self.REUSE_POLICY['allowed_instance_types']}")

        elif resource_type == 'vpc':
            raise Exception("❌ 资源复用策略：禁止创建新VPC，请使用默认VPC！")

        elif resource_type == 'subnet':
            raise Exception("❌ 资源复用策略：禁止创建新子网，请使用现有子网！")

        return True
    
    def generate_resource_report(self):
        """生成资源使用报告"""
        print("📊 生成资源使用报告...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'instances': [],
            'eips': [],
            'volumes': [],
            'cost_estimate': 0
        }
        
        try:
            # 统计实例
            instances = self.ec2.describe_instances()
            for reservation in instances['Reservations']:
                for instance in reservation['Instances']:
                    if instance['State']['Name'] != 'terminated':
                        report['instances'].append({
                            'id': instance['InstanceId'],
                            'name': self._get_instance_name(instance),
                            'type': instance['InstanceType'],
                            'state': instance['State']['Name'],
                            'launch_time': instance['LaunchTime'].isoformat()
                        })
            
            # 统计弹性IP
            eips = self.ec2.describe_addresses()
            for eip in eips['Addresses']:
                report['eips'].append({
                    'ip': eip['PublicIp'],
                    'instance_id': eip.get('InstanceId', '未关联')
                })
            
            # 统计EBS卷
            volumes = self.ec2.describe_volumes()
            for volume in volumes['Volumes']:
                report['volumes'].append({
                    'id': volume['VolumeId'],
                    'size': volume['Size'],
                    'state': volume['State'],
                    'type': volume['VolumeType']
                })
            
            # 估算成本
            running_instances = len([i for i in report['instances'] if i['state'] == 'running'])
            attached_eips = len([e for e in report['eips'] if e['instance_id'] != '未关联'])
            total_volume_size = sum([v['size'] for v in report['volumes']])
            
            # 简单成本估算（美元/月）
            report['cost_estimate'] = {
                'instances': running_instances * 7.5,  # t3.micro约$7.5/月
                'eips': attached_eips * 3.6,  # $3.6/月
                'storage': total_volume_size * 0.1,  # $0.1/GB/月
                'total': running_instances * 7.5 + attached_eips * 3.6 + total_volume_size * 0.1
            }
            
            print(f"✅ 资源统计:")
            print(f"  实例: {len(report['instances'])} 个 (运行中: {running_instances})")
            print(f"  弹性IP: {len(report['eips'])} 个 (已关联: {attached_eips})")
            print(f"  EBS卷: {len(report['volumes'])} 个 (总计: {total_volume_size}GB)")
            print(f"  预估月费用: ${report['cost_estimate']['total']:.2f}")
            
            return report
            
        except Exception as e:
            print(f"❌ 生成报告失败: {e}")
            return report

def main():
    """主函数"""
    print("🚀 AWS资源管理器")
    print("=" * 50)
    
    manager = AWSResourceManager()
    
    # 1. 查找可复用实例
    reusable_instance = manager.find_reusable_instance()
    
    if reusable_instance:
        # 2. 清理重复实例
        deleted_count = manager.cleanup_duplicate_instances(reusable_instance['id'])
        
        # 3. 优化实例使用
        manager.optimize_instance_usage(reusable_instance['id'])
        
        # 4. 更新部署信息
        deployment_info = {
            'instance_id': reusable_instance['id'],
            'public_ip': reusable_instance['public_ip'],
            'vpc_id': reusable_instance['vpc_id'],
            'subnet_id': reusable_instance['subnet_id'],
            'security_group_id': reusable_instance['security_groups'][0] if reusable_instance['security_groups'] else None,
            'region': manager.region,
            'last_optimized': datetime.now().isoformat(),
            'resource_reused': True
        }
        
        with open('vpn_deployment_info.json', 'w') as f:
            json.dump(deployment_info, f, indent=2, default=str)
        
        print(f"✅ 部署信息已更新，复用实例: {reusable_instance['id']}")
    
    # 5. 清理未使用资源
    cleanup_results = manager.cleanup_unused_resources()
    
    # 6. 生成资源报告
    report = manager.generate_resource_report()
    
    print("\n" + "=" * 50)
    print("📊 资源管理完成")
    print(f"删除重复实例: {deleted_count if reusable_instance else 0} 个")
    print(f"清理弹性IP: {cleanup_results['eips']} 个")
    print(f"清理EBS卷: {cleanup_results['volumes']} 个")
    print(f"预估月费用: ${report['cost_estimate']['total']:.2f}")

if __name__ == "__main__":
    main()
