# SSH连接问题最终修复指南

## 🚨 问题确认

**根本原因**: 用户数据脚本没有正确执行，导致SSH密钥配置不正确

**当前状态**:
- 实例ID: `i-0815fe69fa99e7bd0`
- 公网IP: `*************`
- SSH连接: ❌ 失败 (Permission denied)

## 🔧 解决方案

### 方案1: AWS控制台手动修复 (推荐)

1. **登录AWS控制台**
   - 进入EC2服务
   - 找到实例 `i-0815fe69fa99e7bd0`
   - 点击"连接" → "EC2 Instance Connect"

2. **执行修复命令**
```bash
# 切换到root用户
sudo su -

# 创建.ssh目录
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# 从实例元数据获取公钥
curl -s http://***************/latest/meta-data/public-keys/0/openssh-key > /home/<USER>/.ssh/authorized_keys

# 设置正确权限
chmod 600 /home/<USER>/.ssh/authorized_keys
chown -R ec2-user:ec2-user /home/<USER>/.ssh

# 确保ec2-user存在并有sudo权限
id ec2-user || useradd -m -s /bin/bash ec2-user
echo "ec2-user ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/90-ec2-user
chmod 440 /etc/sudoers.d/90-ec2-user

# 重启SSH服务
systemctl restart sshd
systemctl status sshd

# 验证配置
ls -la /home/<USER>/.ssh/
cat /home/<USER>/.ssh/authorized_keys
```

3. **验证修复**
```bash
# 在本地测试SSH连接
ssh -i Google-VPN-Key.pem -o StrictHostKeyChecking=no ec2-user@*************
```

### 方案2: Session Manager修复

如果实例配置了IAM角色，可以通过Session Manager连接：

1. 在AWS控制台中选择实例
2. 点击"连接" → "Session Manager"
3. 执行上述修复命令

### 方案3: 重新应用用户数据

1. **停止实例**
```bash
aws ec2 stop-instances --instance-ids i-0815fe69fa99e7bd0
```

2. **修改用户数据**
```bash
# 创建修复脚本
cat > ssh_fix_userdata.sh << 'EOF'
#!/bin/bash
exec > /var/log/ssh-fix.log 2>&1
echo "开始SSH修复..." 
date

# 确保ec2-user存在
if ! id ec2-user &>/dev/null; then
    useradd -m -s /bin/bash ec2-user
fi

# 配置sudo权限
echo "ec2-user ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/90-ec2-user
chmod 440 /etc/sudoers.d/90-ec2-user

# 创建.ssh目录
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# 配置authorized_keys
curl -s http://***************/latest/meta-data/public-keys/0/openssh-key > /home/<USER>/.ssh/authorized_keys
chmod 600 /home/<USER>/.ssh/authorized_keys
chown -R ec2-user:ec2-user /home/<USER>/.ssh

# 重启SSH服务
systemctl restart sshd

echo "SSH修复完成"
date
EOF

# 编码用户数据
USER_DATA=$(base64 -i ssh_fix_userdata.sh)

# 应用用户数据
aws ec2 modify-instance-attribute \
    --instance-id i-0815fe69fa99e7bd0 \
    --user-data Value="$USER_DATA"
```

3. **启动实例**
```bash
aws ec2 start-instances --instance-ids i-0815fe69fa99e7bd0
```

## 🔍 故障排除

### 检查实例状态
```bash
aws ec2 describe-instances --instance-ids i-0815fe69fa99e7bd0 \
    --query 'Reservations[0].Instances[0].{State:State.Name,IP:PublicIpAddress}'
```

### 检查安全组
```bash
aws ec2 describe-security-groups --group-ids sg-0dcae9751e0bb4127 \
    --query 'SecurityGroups[0].IpPermissions[?FromPort==`22`]'
```

### 检查密钥指纹
```bash
# 本地密钥指纹
ssh-keygen -l -f Google-VPN-Key.pem

# AWS密钥指纹
aws ec2 describe-key-pairs --key-names Google-VPN-Key \
    --query 'KeyPairs[0].KeyFingerprint'
```

### 检查控制台输出
```bash
aws ec2 get-console-output --instance-id i-0815fe69fa99e7bd0 \
    --query 'Output' --output text
```

## 🛠️ 预防措施

### 1. 改进用户数据脚本
确保用户数据脚本包含完整的SSH配置：

```bash
#!/bin/bash
exec > /var/log/user-data.log 2>&1
set -x

# 等待系统完全启动
sleep 30

# 确保基础包已安装
yum update -y
yum install -y curl wget

# 配置ec2-user
if ! id ec2-user &>/dev/null; then
    useradd -m -s /bin/bash ec2-user
fi

# 配置sudo权限
echo "ec2-user ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/90-ec2-user
chmod 440 /etc/sudoers.d/90-ec2-user

# 配置SSH
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# 多次尝试获取公钥
for i in {1..5}; do
    curl -s http://***************/latest/meta-data/public-keys/0/openssh-key > /home/<USER>/.ssh/authorized_keys
    if [ -s /home/<USER>/.ssh/authorized_keys ]; then
        break
    fi
    sleep 10
done

# 设置权限
chmod 600 /home/<USER>/.ssh/authorized_keys
chown -R ec2-user:ec2-user /home/<USER>/.ssh

# 重启SSH服务
systemctl restart sshd
systemctl enable sshd

# 记录完成状态
echo "User data script completed successfully" >> /var/log/user-data.log
date >> /var/log/user-data.log
```

### 2. 添加健康检查
```bash
# 创建SSH健康检查脚本
cat > /usr/local/bin/ssh-health-check.sh << 'EOF'
#!/bin/bash
# SSH健康检查脚本

LOG_FILE="/var/log/ssh-health-check.log"

echo "$(date): Starting SSH health check" >> $LOG_FILE

# 检查SSH服务状态
if ! systemctl is-active --quiet sshd; then
    echo "$(date): SSH service is not running, restarting..." >> $LOG_FILE
    systemctl restart sshd
fi

# 检查authorized_keys文件
if [ ! -f /home/<USER>/.ssh/authorized_keys ] || [ ! -s /home/<USER>/.ssh/authorized_keys ]; then
    echo "$(date): authorized_keys missing or empty, recreating..." >> $LOG_FILE
    mkdir -p /home/<USER>/.ssh
    curl -s http://***************/latest/meta-data/public-keys/0/openssh-key > /home/<USER>/.ssh/authorized_keys
    chmod 600 /home/<USER>/.ssh/authorized_keys
    chown -R ec2-user:ec2-user /home/<USER>/.ssh
fi

echo "$(date): SSH health check completed" >> $LOG_FILE
EOF

chmod +x /usr/local/bin/ssh-health-check.sh

# 添加到crontab
echo "*/5 * * * * /usr/local/bin/ssh-health-check.sh" | crontab -
```

## 📋 验证清单

修复完成后，请验证以下项目：

- [ ] SSH服务正在运行: `systemctl status sshd`
- [ ] ec2-user用户存在: `id ec2-user`
- [ ] .ssh目录权限正确: `ls -la /home/<USER>/.ssh/`
- [ ] authorized_keys文件存在且有内容: `cat /home/<USER>/.ssh/authorized_keys`
- [ ] 文件权限正确: `ls -la /home/<USER>/.ssh/authorized_keys`
- [ ] sudo权限配置正确: `sudo -l -U ec2-user`
- [ ] 安全组允许SSH: 端口22开放
- [ ] 本地SSH连接成功: `ssh -i Google-VPN-Key.pem ec2-user@*************`

## 🎯 预期结果

修复成功后，应该能够：

1. **SSH连接成功**
```bash
$ ssh -i Google-VPN-Key.pem ec2-user@*************
[ec2-user@ip-172-31-x-x ~]$ whoami
ec2-user
```

2. **VPN服务正常**
```bash
$ sudo systemctl status strongswan
● strongswan.service - strongSwan IPsec IKEv1/IKEv2 daemon using ipsec.conf
   Loaded: loaded
   Active: active (running)
```

3. **网络连通性正常**
```bash
$ curl -s https://api.ipify.org
*************
```

---

**重要提醒**: SSH问题的根本原因是用户数据脚本执行失败，建议优先使用AWS控制台的EC2 Instance Connect进行手动修复，这是最可靠的方法。
