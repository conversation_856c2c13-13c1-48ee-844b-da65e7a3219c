import boto3
import json
from botocore.exceptions import ClientError
import logging
import sys
import os

# --- Configuration ---
LOGGING_LEVEL = logging.INFO
DEPLOYMENT_INFO_FILE = 'vpn_deployment_info.json'

# --- Setup Logging ---
logging.basicConfig(level=LOGGING_LEVEL, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_ssm_command(ssm_client, instance_id, commands, comment):
    """Helper function to run a command via SSM and return the output."""
    logger.info(f"Running SSM command: {comment}")
    try:
        response = ssm_client.send_command(
            InstanceIds=[instance_id],
            DocumentName='AWS-RunShellScript',
            Parameters={'commands': commands},
            TimeoutSeconds=120,
            Comment=comment
        )
        command_id = response['Command']['CommandId']
        
        waiter = ssm_client.get_waiter('command_executed')
        waiter.wait(CommandId=command_id, InstanceId=instance_id, WaiterConfig={'Delay': 5, 'MaxAttempts': 24})
        
        output = ssm_client.get_command_invocation(CommandId=command_id, InstanceId=instance_id)
        
        print(f"\n--- START: {comment} ---")
        if output['Status'] == 'Success':
            logger.info(f"Command '{comment}' executed successfully.")
            print(output.get('StandardOutputContent', ''))
        else:
            logger.error(f"Command '{comment}' failed with status: {output['Status']}")
            print("--- STDOUT ---")
            print(output.get('StandardOutputContent', ''))
            print("--- STDERR ---")
            print(output.get('StandardErrorContent', ''))
        print(f"--- END: {comment} ---\n")
        return output
        
    except Exception as e:
        logger.error(f"An error occurred while running SSM command '{comment}': {e}")
        return None

def main():
    """Main function."""
    logger.info("--- Server Deep Scan ---")
    
    # Load deployment info
    if not os.path.exists(DEPLOYMENT_INFO_FILE):
        logger.error(f"Deployment info file '{DEPLOYMENT_INFO_FILE}' not found.")
        sys.exit(1)
    with open(DEPLOYMENT_INFO_FILE, 'r') as f:
        deployment_info = json.load(f)
    
    instance_id = deployment_info.get('instance_id')
    region = deployment_info.get('region')
    
    if not instance_id or not region:
        logger.error("Instance ID or region not found in deployment info file.")
        sys.exit(1)
        
    ssm_client = boto3.client('ssm', region_name=region)

    # 1. Check if firewalld package is installed
    run_ssm_command(ssm_client, instance_id, ['rpm -q firewalld'], "Check firewalld package")

    # 2. Check firewalld service status (active and enabled)
    run_ssm_command(ssm_client, instance_id, ['systemctl is-active firewalld', 'systemctl is-enabled firewalld'], "Check firewalld service status")

    # 3. Get firewalld rules
    run_ssm_command(ssm_client, instance_id, ['firewall-cmd --list-all'], "Get firewalld rules")
    
    # 4. Check for listening ports
    run_ssm_command(ssm_client, instance_id, ['ss -tuln'], "Check listening ports")

if __name__ == "__main__":
    main()
