# Windows VPN连接测试脚本
# 测试IKEv2连接到VPN服务器

param(
    [string]$ServerIP = "*************",
    [string]$VPNName = "TestVPN-IKEv2",
    [string]$Username = "vpnuser",
    [string]$Password = "GoogleVPN2023!",
    [string]$PSK = "GoogleVPN2023!"
)

Write-Host "=== Windows VPN连接测试 ===" -ForegroundColor Green
Write-Host "服务器: $ServerIP" -ForegroundColor Cyan
Write-Host "VPN名称: $VPNName" -ForegroundColor Cyan

# 函数：检查是否以管理员身份运行
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 检查管理员权限
if (-not (Test-Administrator)) {
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请以管理员身份重新运行PowerShell" -ForegroundColor Yellow
    exit 1
}

# 函数：删除现有VPN连接
function Remove-ExistingVPN {
    param([string]$Name)
    
    try {
        $existing = Get-VpnConnection -Name $Name -ErrorAction SilentlyContinue
        if ($existing) {
            Write-Host "🗑️ 删除现有VPN连接: $Name" -ForegroundColor Yellow
            Remove-VpnConnection -Name $Name -Force
        }
    } catch {
        # 忽略错误
    }
}

# 函数：创建IKEv2 VPN连接
function Add-IKEv2VPN {
    param(
        [string]$Name,
        [string]$Server,
        [string]$User,
        [string]$Pass
    )
    
    try {
        Write-Host "🔧 创建IKEv2 VPN连接..." -ForegroundColor Yellow
        
        # 删除现有连接
        Remove-ExistingVPN -Name $Name
        
        # 创建新的VPN连接
        Add-VpnConnection -Name $Name -ServerAddress $Server -TunnelType IKEv2 -AuthenticationMethod EAP -EncryptionLevel Required
        
        Write-Host "✅ VPN连接已创建" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ 创建VPN连接失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 函数：测试VPN连接
function Test-VPNConnection {
    param(
        [string]$Name,
        [string]$User,
        [string]$Pass
    )
    
    try {
        Write-Host "🔌 尝试连接VPN..." -ForegroundColor Yellow
        
        # 设置凭据
        $securePassword = ConvertTo-SecureString $Pass -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($User, $securePassword)
        
        # 连接VPN
        rasdial $Name $User $Pass
        
        Start-Sleep -Seconds 5
        
        # 检查连接状态
        $connection = Get-VpnConnection -Name $Name
        if ($connection.ConnectionStatus -eq "Connected") {
            Write-Host "✅ VPN连接成功！" -ForegroundColor Green
            
            # 测试IP地址
            Test-IPAddress
            
            # 断开连接
            Write-Host "🔌 断开VPN连接..." -ForegroundColor Yellow
            rasdial $Name /disconnect
            
            return $true
        } else {
            Write-Host "❌ VPN连接失败" -ForegroundColor Red
            return $false
        }
        
    } catch {
        Write-Host "❌ VPN连接测试失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 函数：测试IP地址
function Test-IPAddress {
    Write-Host "🔍 检查公网IP地址..." -ForegroundColor Yellow
    
    try {
        $ip = (Invoke-WebRequest -Uri "https://api.ipify.org" -UseBasicParsing -TimeoutSec 10).Content
        Write-Host "当前公网IP: $ip" -ForegroundColor Cyan
        
        if ($ip -eq $ServerIP) {
            Write-Host "✅ IP地址正确，VPN连接有效" -ForegroundColor Green
        } else {
            Write-Host "⚠️ IP地址不匹配，可能VPN未生效" -ForegroundColor Yellow
        }
        
        # 测试Google访问
        Write-Host "🔍 测试Google服务访问..." -ForegroundColor Yellow
        try {
            $response = Invoke-WebRequest -Uri "https://www.google.com" -UseBasicParsing -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ Google访问正常" -ForegroundColor Green
            }
        } catch {
            Write-Host "❌ Google访问失败" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "❌ 无法获取IP地址" -ForegroundColor Red
    }
}

# 函数：测试端口连通性
function Test-VPNPorts {
    Write-Host "🔍 测试VPN端口连通性..." -ForegroundColor Yellow
    
    $ports = @(500, 4500, 1701)
    foreach ($port in $ports) {
        $result = Test-NetConnection -ComputerName $ServerIP -Port $port -WarningAction SilentlyContinue
        if ($result.TcpTestSucceeded) {
            Write-Host "✅ 端口 $port 可达" -ForegroundColor Green
        } else {
            Write-Host "❌ 端口 $port 不可达" -ForegroundColor Red
        }
    }
}

# 主程序
Write-Host "`n开始VPN连接测试..." -ForegroundColor Green

# 1. 测试端口连通性
Test-VPNPorts

# 2. 创建VPN连接
if (Add-IKEv2VPN -Name $VPNName -Server $ServerIP -User $Username -Pass $Password) {
    
    # 3. 测试VPN连接
    if (Test-VPNConnection -Name $VPNName -User $Username -Pass $Password) {
        Write-Host "`n✅ VPN测试成功！" -ForegroundColor Green
    } else {
        Write-Host "`n❌ VPN测试失败" -ForegroundColor Red
    }
    
    # 4. 清理测试连接
    Write-Host "`n🗑️ 清理测试连接..." -ForegroundColor Yellow
    Remove-ExistingVPN -Name $VPNName
    
} else {
    Write-Host "`n❌ 无法创建VPN连接" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
