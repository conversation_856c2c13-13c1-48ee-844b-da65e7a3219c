#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN服务状态检查脚本
检查VPN服务器的运行状态和网络连通性
"""

import boto3
import json
import subprocess
import socket
import sys
import os
from botocore.exceptions import ClientError

def load_deployment_info():
    """加载部署信息"""
    try:
        with open('vpn_deployment_info.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 错误：未找到部署信息文件")
        return None
    except Exception as e:
        print(f"❌ 错误：读取部署信息失败: {e}")
        return None

def check_instance_status(deployment_info):
    """检查EC2实例状态"""
    print("=== 检查EC2实例状态 ===")
    
    try:
        ec2 = boto3.client('ec2', region_name=deployment_info['region'])
        instance_id = deployment_info['instance_id']
        
        response = ec2.describe_instances(InstanceIds=[instance_id])
        if not response['Reservations']:
            print(f"❌ 实例 {instance_id} 不存在")
            return False
        
        instance = response['Reservations'][0]['Instances'][0]
        state = instance['State']['Name']
        public_ip = instance.get('PublicIpAddress', 'N/A')
        
        print(f"实例ID: {instance_id}")
        print(f"状态: {state}")
        print(f"公网IP: {public_ip}")
        
        if state == 'running':
            print("✅ 实例运行正常")
            return True
        else:
            print(f"⚠️  实例状态异常: {state}")
            return False
            
    except Exception as e:
        print(f"❌ 检查实例状态失败: {e}")
        return False

def check_network_connectivity(public_ip):
    """检查网络连通性"""
    print(f"\n=== 检查网络连通性 ===")
    
    # 检查SSH端口 (22)
    print("检查SSH端口 (22)...")
    if check_port(public_ip, 22):
        print("✅ SSH端口 (22) 可达")
    else:
        print("❌ SSH端口 (22) 不可达")
    
    # 检查IPSec端口 (500, 4500)
    print("检查IPSec端口 (500)...")
    if check_port(public_ip, 500, 'udp'):
        print("✅ IPSec端口 (500/UDP) 可达")
    else:
        print("❌ IPSec端口 (500/UDP) 不可达")
    
    print("检查IPSec端口 (4500)...")
    if check_port(public_ip, 4500, 'udp'):
        print("✅ IPSec端口 (4500/UDP) 可达")
    else:
        print("❌ IPSec端口 (4500/UDP) 不可达")

def check_port(host, port, protocol='tcp', timeout=5):
    """检查端口连通性"""
    try:
        if protocol.lower() == 'tcp':
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        else:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        
        return result == 0
    except Exception:
        return False

def check_ssh_connectivity(public_ip):
    """检查SSH连接和VPN服务状态"""
    print(f"\n=== 检查VPN服务状态 ===")
    
    key_file = "Google-VPN-Key.pem"
    if not os.path.exists(key_file):
        print(f"❌ 密钥文件 {key_file} 不存在")
        return False
    
    # 设置密钥文件权限（Windows下可能不需要）
    try:
        if os.name != 'nt':  # 非Windows系统
            os.chmod(key_file, 0o600)
    except Exception:
        pass
    
    # SSH命令列表
    commands = [
        ("StrongSwan服务状态", "sudo systemctl status strongswan --no-pager"),
        ("StrongSwan是否启用", "sudo systemctl is-enabled strongswan"),
        ("IPSec状态", "sudo ipsec status"),
        ("网络接口状态", "ip addr show"),
        ("防火墙规则", "sudo iptables -L -n"),
        ("系统负载", "uptime"),
        ("磁盘使用", "df -h"),
        ("内存使用", "free -h")
    ]
    
    print(f"通过SSH连接到 {public_ip} 检查服务状态...")
    
    for desc, cmd in commands:
        print(f"\n--- {desc} ---")
        try:
            ssh_cmd = [
                "ssh", 
                "-i", key_file,
                "-o", "StrictHostKeyChecking=no",
                "-o", "ConnectTimeout=10",
                f"ec2-user@{public_ip}",
                cmd
            ]
            
            result = subprocess.run(
                ssh_cmd, 
                capture_output=True, 
                text=True, 
                timeout=30
            )
            
            if result.returncode == 0:
                print(result.stdout.strip())
            else:
                print(f"命令执行失败 (返回码: {result.returncode})")
                if result.stderr:
                    print(f"错误信息: {result.stderr.strip()}")
                    
        except subprocess.TimeoutExpired:
            print("命令执行超时")
        except FileNotFoundError:
            print("❌ SSH命令不可用，请确保已安装OpenSSH客户端")
            return False
        except Exception as e:
            print(f"执行命令时出错: {e}")
    
    return True

def generate_vpn_client_config(deployment_info):
    """生成VPN客户端配置信息"""
    print(f"\n=== VPN客户端配置信息 ===")

    public_ip = deployment_info['public_ip']

    # 读取预共享密钥
    try:
        import configparser
        config = configparser.ConfigParser()
        if os.path.exists('vpn_config.ini'):
            config.read('vpn_config.ini')
            psk = config.get('VPN', 'PreSharedKey', fallback='GoogleVPN2023!')
        else:
            psk = 'GoogleVPN2023!'
    except Exception:
        psk = 'GoogleVPN2023!'

    print("🔧 支持的VPN协议:")
    print("1. IKEv2 (推荐) - 高性能，低延迟")
    print("2. L2TP/IPSec (备用) - 兼容性好")

    print(f"\n📱 Windows 连接方法:")
    print(f"方法1 - IKEv2:")
    print(f"  服务器地址: {public_ip}")
    print(f"  VPN类型: IKEv2")
    print(f"  认证方式: 用户名和密码")
    print(f"  用户名: vpnuser")
    print(f"  密码: {psk}")

    print(f"\n方法2 - L2TP/IPSec:")
    print(f"  服务器地址: {public_ip}")
    print(f"  VPN类型: L2TP/IPSec (预共享密钥)")
    print(f"  预共享密钥: {psk}")
    print(f"  用户名: vpnuser")
    print(f"  密码: {psk}")

    print(f"\n📱 移动设备配置:")
    print(f"iOS/Android:")
    print(f"  类型: IKEv2")
    print(f"  服务器: {public_ip}")
    print(f"  远程ID: {public_ip}")
    print(f"  用户名: vpnuser")
    print(f"  密码: {psk}")

    print(f"\n✅ 连接验证:")
    print(f"连接成功后访问: https://whatismyipaddress.com")
    print(f"应显示IP: {public_ip} (美国弗吉尼亚州)")
    print(f"然后可访问: https://aistudio.google.com")

    # 生成配置文件
    config_content = f"""# VPN客户端配置指南

## 服务器信息
- 服务器地址: {public_ip}
- 预共享密钥: {psk}
- 用户名: vpnuser (或 testuser, admin)
- 密码: {psk}

## Windows 连接方法

### 方法1: IKEv2 (推荐)
1. 设置 → 网络和Internet → VPN → 添加VPN连接
2. 配置参数:
   - VPN提供商: Windows (内置)
   - 连接名称: Google-VPN
   - 服务器: {public_ip}
   - VPN类型: IKEv2
   - 登录信息类型: 用户名和密码
   - 用户名: vpnuser
   - 密码: {psk}

### 方法2: L2TP/IPSec (备用)
1. 设置 → 网络和Internet → VPN → 添加VPN连接
2. 配置参数:
   - VPN提供商: Windows (内置)
   - 连接名称: Google-VPN-L2TP
   - 服务器: {public_ip}
   - VPN类型: L2TP/IPSec (预共享密钥)
   - 预共享密钥: {psk}
   - 用户名: vpnuser
   - 密码: {psk}

## 移动设备连接

### iOS
1. 设置 → 通用 → VPN → 添加VPN配置
2. 类型: IKEv2
3. 服务器: {public_ip}
4. 远程ID: {public_ip}
5. 用户名: vpnuser
6. 密码: {psk}

### Android
1. 设置 → 网络和互联网 → VPN → 添加VPN
2. 类型: IKEv2/IPSec PSK
3. 服务器地址: {public_ip}
4. IPSec标识符: {public_ip}
5. 预共享密钥: {psk}

## 连接验证
连接成功后访问: https://whatismyipaddress.com
应显示IP: {public_ip} (美国弗吉尼亚州)
"""

    with open('VPN客户端配置指南.md', 'w', encoding='utf-8') as f:
        f.write(config_content)

    print(f"\n📄 已生成配置文件: VPN客户端配置指南.md")

def main():
    print("VPN服务状态检查工具")
    print("==================")
    
    # 加载部署信息
    deployment_info = load_deployment_info()
    if not deployment_info:
        return
    
    print(f"检查区域: {deployment_info['region']}")
    print(f"目标服务器: {deployment_info['public_ip']}")
    
    # 1. 检查实例状态
    instance_ok = check_instance_status(deployment_info)
    
    if not instance_ok:
        print("\n❌ 实例状态异常，无法继续检查")
        return
    
    # 2. 检查网络连通性
    check_network_connectivity(deployment_info['public_ip'])
    
    # 3. 检查SSH连接和服务状态
    ssh_ok = check_ssh_connectivity(deployment_info['public_ip'])
    
    # 4. 生成客户端配置信息
    generate_vpn_client_config(deployment_info)
    
    print(f"\n{'='*50}")
    print("检查完成")
    print(f"{'='*50}")
    
    if instance_ok and ssh_ok:
        print("✅ VPN服务检查完成，服务运行正常")
    else:
        print("⚠️  VPN服务检查发现问题，请查看上述详细信息")

if __name__ == "__main__":
    main()
