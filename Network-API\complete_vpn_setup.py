#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的VPN安装和配置
"""

import boto3
import json
import time

def send_command(ssm, instance_id, commands, description):
    """发送单个命令"""
    try:
        print(f"\n🔧 {description}")
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={'commands': commands},
            TimeoutSeconds=120
        )
        
        command_id = response['Command']['CommandId']
        
        # 等待命令完成
        for i in range(30):  # 等待最多30秒
            time.sleep(4)
            try:
                result = ssm.get_command_invocation(
                    CommandId=command_id,
                    InstanceId=instance_id
                )
                
                status = result['Status']
                if status == 'Success':
                    output = result['StandardOutputContent']
                    if output.strip():
                        print(f"✅ 成功: {output[-500:]}")  # 只显示最后500字符
                    else:
                        print("✅ 成功")
                    return True
                elif status == 'Failed':
                    error = result['StandardErrorContent']
                    print(f"❌ 失败: {error}")
                    return False
                elif status in ['InProgress', 'Pending']:
                    continue
                    
            except Exception as e:
                if 'InvocationDoesNotExist' in str(e):
                    continue
                else:
                    print(f"❌ 错误: {e}")
                    return False
        
        print("⚠️ 超时")
        return False
        
    except Exception as e:
        print(f"❌ 发送命令失败: {e}")
        return False

def main():
    try:
        # 读取部署信息
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        instance_id = deployment_info['instance_id']
        region = deployment_info['region']
        
        print(f"实例ID: {instance_id}")
        
        # 创建SSM客户端
        ssm = boto3.client('ssm', region_name=region)
        
        # 步骤1: 检查系统版本和安装EPEL
        send_command(ssm, instance_id, [
            'cat /etc/os-release',
            'yum update -y',
            'yum install -y epel-release'
        ], "检查系统版本并安装EPEL")
        
        # 步骤2: 安装VPN软件包
        send_command(ssm, instance_id, [
            'yum install -y libreswan xl2tpd ppp iptables-services'
        ], "安装VPN软件包")
        
        # 步骤3: 获取公网IP并配置系统参数
        send_command(ssm, instance_id, [
            'PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)',
            'echo "公网IP: $PUBLIC_IP"',
            'echo "net.ipv4.ip_forward = 1" >> /etc/sysctl.conf',
            'echo "net.ipv4.conf.all.accept_redirects = 0" >> /etc/sysctl.conf',
            'echo "net.ipv4.conf.all.send_redirects = 0" >> /etc/sysctl.conf',
            'sysctl -p'
        ], "配置系统参数")
        
        # 步骤4: 配置IPSec
        send_command(ssm, instance_id, [
            'PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)',
            'cat > /etc/ipsec.conf << EOF',
            'version 2.0',
            'config setup',
            '    nat_traversal=yes',
            '    virtual_private=%v4:10.0.0.0/8,%v4:***********/16,%v4:**********/12',
            '    oe=off',
            '    protostack=netkey',
            '    nhelpers=0',
            '    interfaces=%defaultroute',
            '',
            'conn vpnpsk',
            '    connaddrfamily=ipv4',
            '    auto=add',
            '    left=%defaultroute',
            '    leftid=$PUBLIC_IP',
            '    leftsubnet=0.0.0.0/0',
            '    leftprotoport=17/1701',
            '    rightprotoport=17/%any',
            '    right=%any',
            '    rightsubnetwithin=0.0.0.0/0',
            '    forceencaps=yes',
            '    authby=secret',
            '    pfs=no',
            '    type=transport',
            '    auth=esp',
            '    ike=3des-sha1,aes-sha1',
            '    phase2alg=3des-sha1,aes-sha1',
            '    dpddelay=40',
            '    dpdtimeout=130',
            '    dpdaction=clear',
            'EOF'
        ], "配置IPSec")
        
        # 步骤5: 配置IPSec密钥
        send_command(ssm, instance_id, [
            'PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)',
            'cat > /etc/ipsec.secrets << EOF',
            '$PUBLIC_IP %any : PSK "GoogleVPN2023!"',
            'EOF'
        ], "配置IPSec密钥")
        
        # 步骤6: 配置L2TP
        send_command(ssm, instance_id, [
            'mkdir -p /etc/xl2tpd',
            'cat > /etc/xl2tpd/xl2tpd.conf << EOF',
            '[global]',
            'port = 1701',
            '',
            '[lns default]',
            'ip range = ***********-************',
            'local ip = **********',
            'require chap = yes',
            'refuse pap = yes',
            'require authentication = yes',
            'name = l2tpd',
            'ppp debug = yes',
            'pppoptfile = /etc/ppp/options.xl2tpd',
            'length bit = yes',
            'EOF'
        ], "配置L2TP")
        
        # 步骤7: 配置PPP
        send_command(ssm, instance_id, [
            'cat > /etc/ppp/options.xl2tpd << EOF',
            'ipcp-accept-local',
            'ipcp-accept-remote',
            'ms-dns *******',
            'ms-dns *******',
            'noccp',
            'auth',
            'crtscts',
            'idle 1800',
            'mtu 1280',
            'mru 1280',
            'lock',
            'connect-delay 5000',
            'EOF'
        ], "配置PPP")
        
        # 步骤8: 配置用户认证
        send_command(ssm, instance_id, [
            'cat > /etc/ppp/chap-secrets << EOF',
            'vpnuser l2tpd GoogleVPN2023! *',
            'EOF'
        ], "配置用户认证")
        
        # 步骤9: 启动服务
        send_command(ssm, instance_id, [
            'systemctl enable ipsec',
            'systemctl enable xl2tpd',
            'systemctl start ipsec',
            'systemctl start xl2tpd'
        ], "启动VPN服务")
        
        # 步骤10: 配置防火墙
        send_command(ssm, instance_id, [
            'systemctl enable iptables',
            'systemctl start iptables',
            'iptables -t nat -A POSTROUTING -s **********/24 -o eth0 -j MASQUERADE',
            'iptables -A INPUT -p udp --dport 500 -j ACCEPT',
            'iptables -A INPUT -p udp --dport 4500 -j ACCEPT',
            'iptables -A INPUT -p udp --dport 1701 -j ACCEPT',
            'iptables -A FORWARD -s **********/24 -j ACCEPT',
            'iptables -A FORWARD -d **********/24 -j ACCEPT',
            'service iptables save'
        ], "配置防火墙")
        
        # 步骤11: 检查最终状态
        send_command(ssm, instance_id, [
            'systemctl status ipsec --no-pager',
            'systemctl status xl2tpd --no-pager',
            'ss -unp | grep -E ":(500|1701|4500)"'
        ], "检查最终状态")
        
        print("\n✅ VPN完整配置完成！")
        print("请运行 'python test_vpn_connections.py' 测试连接")
        
    except Exception as e:
        print(f"❌ 配置失败: {e}")

if __name__ == "__main__":
    print("=== 完整VPN配置工具 ===")
    main()
