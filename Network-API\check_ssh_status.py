import boto3
import json
from botocore.exceptions import ClientError
import logging
import sys
import os

# --- Configuration ---
LOGGING_LEVEL = logging.INFO
DEPLOYMENT_INFO_FILE = 'vpn_deployment_info.json'

# --- Setup Logging ---
logging.basicConfig(level=LOGGING_LEVEL, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_service_status(instance_id, region, service_name):
    """Uses SSM to check the status of a service on the instance."""
    logger.info(f"Attempting to check status of '{service_name}' on instance '{instance_id}' via SSM.")
    
    try:
        ssm_client = boto3.client('ssm', region_name=region)
        
        command = f"systemctl status {service_name}"
        
        response = ssm_client.send_command(
            InstanceIds=[instance_id],
            DocumentName='AWS-RunShellScript',
            Parameters={'commands': [command]},
            TimeoutSeconds=60,
            Comment=f'Check status of {service_name}'
        )
        
        command_id = response['Command']['CommandId']
        logger.info(f"SSM command sent. Command ID: {command_id}. Waiting for completion...")
        
        waiter = ssm_client.get_waiter('command_executed')
        waiter.wait(
            CommandId=command_id,
            InstanceId=instance_id,
            WaiterConfig={'Delay': 5, 'MaxAttempts': 12}
        )
        
        output_response = ssm_client.get_command_invocation(
            CommandId=command_id,
            InstanceId=instance_id,
        )
        
        status = output_response.get('Status')
        stdout = output_response.get('StandardOutputContent', '')
        stderr = output_response.get('StandardErrorContent', '')

        logger.info(f"Command status: {status}")
        print(f"\n--- Status of '{service_name}' ---")
        print(stdout)
        if stderr:
            print("\n--- Stderr ---")
            print(stderr)
        print("--------------------------")

        if "Active: active (running)" in stdout:
            logger.info(f"✅ Service '{service_name}' is active and running.")
        else:
            logger.warning(f"⚠️ Service '{service_name}' is not active. Check the status output for details.")

    except ClientError as e:
        logger.error(f"An AWS error occurred: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")

def main():
    """Main function."""
    logger.info("--- SSH Service Status Checker ---")
    
    # Load deployment info
    if not os.path.exists(DEPLOYMENT_INFO_FILE):
        logger.error(f"Deployment info file '{DEPLOYMENT_INFO_FILE}' not found.")
        sys.exit(1)
    with open(DEPLOYMENT_INFO_FILE, 'r') as f:
        deployment_info = json.load(f)
    
    instance_id = deployment_info.get('instance_id')
    region = deployment_info.get('region')
    
    if not instance_id or not region:
        logger.error("Instance ID or region not found in deployment info file.")
        sys.exit(1)
        
    check_service_status(instance_id, region, 'sshd')

if __name__ == "__main__":
    main()
