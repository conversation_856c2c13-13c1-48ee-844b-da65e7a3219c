# VPN客户端算法修复脚本
# 以管理员身份运行此脚本

Write-Host "🔧 VPN客户端算法修复工具" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 请以管理员身份运行此脚本" -ForegroundColor Red
    Write-Host "右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ 管理员权限确认" -ForegroundColor Green

# 注册表路径
$regPath = "HKLM:\SYSTEM\CurrentControlSet\Services\RasMan\Parameters"

Write-Host "📝 正在修复VPN客户端算法配置..." -ForegroundColor Yellow

try {
    # 1. 允许强加密算法
    Write-Host "1. 启用强加密算法..." -ForegroundColor Cyan
    New-ItemProperty -Path $regPath -Name "AllowStrongCrypto" -Value 1 -PropertyType DWORD -Force | Out-Null
    Write-Host "   ✅ 已启用强加密" -ForegroundColor Green

    # 2. 禁用弱加密算法
    Write-Host "2. 禁用弱加密算法..." -ForegroundColor Cyan
    New-ItemProperty -Path $regPath -Name "DisableWeakCrypto" -Value 1 -PropertyType DWORD -Force | Out-Null
    Write-Host "   ✅ 已禁用弱加密" -ForegroundColor Green

    # 3. 设置首选算法
    Write-Host "3. 设置首选算法..." -ForegroundColor Cyan
    New-ItemProperty -Path $regPath -Name "PreferredCrypto" -Value "MODP2048" -PropertyType String -Force | Out-Null
    Write-Host "   ✅ 已设置首选算法为MODP2048" -ForegroundColor Green

    # 4. 设置算法优先级
    Write-Host "4. 设置算法优先级..." -ForegroundColor Cyan
    $algorithms = @(
        "aes256-sha256-modp2048",
        "aes256-sha1-modp2048", 
        "aes128-sha256-modp2048",
        "aes128-sha1-modp2048",
        "aes256-sha1-modp1536",
        "aes128-sha1-modp1536"
    )
    New-ItemProperty -Path $regPath -Name "IKEAlgorithms" -Value ($algorithms -join ",") -PropertyType String -Force | Out-Null
    Write-Host "   ✅ 已设置算法优先级" -ForegroundColor Green

    # 5. 允许NAT穿透
    Write-Host "5. 启用NAT穿透..." -ForegroundColor Cyan
    New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\PolicyAgent" -Name "AssumeUDPEncapsulationContextOnSendRule" -Value 2 -PropertyType DWORD -Force | Out-Null
    Write-Host "   ✅ 已启用NAT穿透" -ForegroundColor Green

    # 6. 重启相关服务
    Write-Host "6. 重启VPN相关服务..." -ForegroundColor Cyan
    try {
        Restart-Service RasMan -Force -ErrorAction Stop
        Write-Host "   ✅ RasMan服务已重启" -ForegroundColor Green
    } catch {
        Write-Host "   ⚠️  RasMan服务重启失败，可能需要手动重启" -ForegroundColor Yellow
    }

    try {
        Restart-Service PolicyAgent -Force -ErrorAction Stop
        Write-Host "   ✅ PolicyAgent服务已重启" -ForegroundColor Green
    } catch {
        Write-Host "   ⚠️  PolicyAgent服务重启失败，可能需要手动重启" -ForegroundColor Yellow
    }

    Write-Host ""
    Write-Host "🎉 VPN客户端算法修复完成！" -ForegroundColor Green
    Write-Host "================================" -ForegroundColor Green
    Write-Host "📋 修复内容:" -ForegroundColor Cyan
    Write-Host "   ✅ 启用强加密算法 (MODP2048, MODP1536)" -ForegroundColor White
    Write-Host "   ✅ 禁用弱加密算法 (MODP1024)" -ForegroundColor White
    Write-Host "   ✅ 设置算法优先级" -ForegroundColor White
    Write-Host "   ✅ 启用NAT穿透" -ForegroundColor White
    Write-Host "   ✅ 重启VPN服务" -ForegroundColor White

    Write-Host ""
    Write-Host "📱 下一步操作:" -ForegroundColor Yellow
    Write-Host "1. 重启计算机 (推荐)" -ForegroundColor White
    Write-Host "2. 删除现有VPN连接" -ForegroundColor White
    Write-Host "3. 重新添加VPN连接:" -ForegroundColor White
    Write-Host "   - 服务器: *************" -ForegroundColor White
    Write-Host "   - 类型: IKEv2" -ForegroundColor White
    Write-Host "   - 认证: 用户名密码" -ForegroundColor White
    Write-Host "   - 用户名: vpnuser" -ForegroundColor White
    Write-Host "   - 密码: GoogleVPN2023!" -ForegroundColor White

    Write-Host ""
    Write-Host "🔍 验证连接:" -ForegroundColor Yellow
    Write-Host "   - 访问 https://whatismyipaddress.com" -ForegroundColor White
    Write-Host "   - 应显示IP: *************" -ForegroundColor White

} catch {
    Write-Host "❌ 修复过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查管理员权限或手动执行修复步骤" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "💡 提示: 如果连接仍有问题，请尝试使用L2TP/IPSec协议" -ForegroundColor Cyan
Write-Host "   服务器: *************" -ForegroundColor White
Write-Host "   预共享密钥: GoogleVPN2023!" -ForegroundColor White 