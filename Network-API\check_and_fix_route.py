import boto3
import json
from botocore.exceptions import ClientError
import logging
import sys
import os

# --- Configuration ---
LOGGING_LEVEL = logging.INFO
DEPLOYMENT_INFO_FILE = 'vpn_deployment_info.json'

# --- Setup Logging ---
logging.basicConfig(level=LOGGING_LEVEL, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_and_fix_internet_route(ec2_client, vpc_id, subnet_id):
    """Checks for a default route to an Internet Gateway and creates it if missing."""
    logger.info(f"Checking route table for subnet '{subnet_id}' in VPC '{vpc_id}'.")
    
    try:
        # Find the route table associated with the subnet
        response = ec2_client.describe_route_tables(
            Filters=[{'Name': 'association.subnet-id', 'Values': [subnet_id]}]
        )
        
        if not response.get('RouteTables'):
            logger.warning(f"No specific route table associated with subnet '{subnet_id}'. Checking main route table for VPC.")
            response = ec2_client.describe_route_tables(
                Filters=[
                    {'Name': 'vpc-id', 'Values': [vpc_id]},
                    {'Name': 'association.main', 'Values': ['true']}
                ]
            )
            if not response.get('RouteTables'):
                 logger.error("Could not find any route table for the subnet or VPC.")
                 return False

        route_table = response['RouteTables'][0]
        route_table_id = route_table['RouteTableId']
        logger.info(f"Found route table: {route_table_id}")

        # Check for an existing default route to an Internet Gateway
        has_igw_route = False
        for route in route_table['Routes']:
            if route.get('DestinationCidrBlock') == '0.0.0.0/0':
                if 'GatewayId' in route and route['GatewayId'].startswith('igw-'):
                    logger.info(f"✅ Found existing default route to Internet Gateway: {route['GatewayId']}")
                    has_igw_route = True
                    break
        
        if has_igw_route:
            return True

        # If no default route exists, create one.
        logger.warning("No default route to an Internet Gateway found. Attempting to create one.")
        
        # First, find the Internet Gateway attached to the VPC
        igw_response = ec2_client.describe_internet_gateways(
            Filters=[{'Name': 'attachment.vpc-id', 'Values': [vpc_id]}]
        )
        
        if not igw_response.get('InternetGateways'):
            logger.error(f"❌ No Internet Gateway is attached to VPC '{vpc_id}'. Cannot create route.")
            return False
            
        internet_gateway_id = igw_response['InternetGateways'][0]['InternetGatewayId']
        logger.info(f"Found Internet Gateway: {internet_gateway_id}")
        
        # Create the route
        ec2_client.create_route(
            RouteTableId=route_table_id,
            DestinationCidrBlock='0.0.0.0/0',
            GatewayId=internet_gateway_id
        )
        
        logger.info(f"✅ Successfully created default route to Internet Gateway '{internet_gateway_id}' in route table '{route_table_id}'.")
        return True

    except ClientError as e:
        logger.error(f"An AWS error occurred: {e}")
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
        return False

def main():
    """Main function."""
    logger.info("--- VPC Route Checker and Fixer ---")
    
    # Load deployment info
    config_path = 'vpn_deployment_info.json'
    if not os.path.exists(config_path):
        logger.error(f"Deployment info file '{config_path}' not found in current directory.")
        sys.exit(1)
    with open(config_path, 'r') as f:
        deployment_info = json.load(f)
    
    vpc_id = deployment_info.get('vpc_id')
    subnet_id = deployment_info.get('subnet_id')
    region = deployment_info.get('region')
    
    if not all([vpc_id, subnet_id, region]):
        logger.error("VPC ID, Subnet ID, or Region not found in deployment info file.")
        sys.exit(1)
        
    ec2_client = boto3.client('ec2', region_name=region)
    
    success = check_and_fix_internet_route(ec2_client, vpc_id, subnet_id)
    
    if success:
        logger.info("✅ Route check/fix process completed successfully.")
    else:
        logger.error("❌ Route check/fix process failed.")

if __name__ == "__main__":
    main()
