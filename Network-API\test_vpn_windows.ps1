# VPN连接测试脚本 - Windows PowerShell
# 测试3种VPN连接方式

param(
    [string]$ServerIP = "*************",
    [string]$Username = "vpnuser",
    [string]$Password = "GoogleVPN2023!",
    [string]$PSK = "GoogleVPN2023!"
)

Write-Host "=== VPN连接测试工具 ===" -ForegroundColor Green
Write-Host "服务器: $ServerIP" -ForegroundColor Yellow
Write-Host "测试时间: $(Get-Date)" -ForegroundColor Yellow

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请以管理员身份运行PowerShell" -ForegroundColor Yellow
    exit 1
}

# 函数：测试网络连通性
function Test-VPNPorts {
    param([string]$Server)
    
    Write-Host "`n=== 测试端口连通性 ===" -ForegroundColor Cyan
    
    $ports = @(
        @{Port=500; Protocol="UDP"; Name="IPSec IKE"},
        @{Port=4500; Protocol="UDP"; Name="IPSec NAT-T"},
        @{Port=1701; Protocol="UDP"; Name="L2TP"}
    )
    
    foreach ($portInfo in $ports) {
        try {
            $result = Test-NetConnection -ComputerName $Server -Port $portInfo.Port -InformationLevel Quiet
            if ($result) {
                Write-Host "✅ $($portInfo.Name) ($($portInfo.Protocol)/$($portInfo.Port)) - 可达" -ForegroundColor Green
            } else {
                Write-Host "❌ $($portInfo.Name) ($($portInfo.Protocol)/$($portInfo.Port)) - 不可达" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ $($portInfo.Name) ($($portInfo.Protocol)/$($portInfo.Port)) - 测试失败" -ForegroundColor Red
        }
    }
}

# 函数：创建IKEv2 EAP连接
function New-IKEv2EAPConnection {
    param([string]$Server, [string]$User, [string]$Pass)
    
    Write-Host "`n=== 测试 IKEv2 + EAP认证 ===" -ForegroundColor Cyan
    
    $connectionName = "Test-IKEv2-EAP"
    
    try {
        # 删除已存在的连接
        Remove-VpnConnection -Name $connectionName -Force -ErrorAction SilentlyContinue
        
        # 创建新连接
        Add-VpnConnection -Name $connectionName -ServerAddress $Server -TunnelType IKEv2 -AuthenticationMethod MSChapv2 -EncryptionLevel Optional -Force
        
        Write-Host "✅ IKEv2 EAP连接已创建" -ForegroundColor Green
        
        # 尝试连接
        Write-Host "🔄 尝试连接..." -ForegroundColor Yellow
        
        # 设置凭据
        $securePassword = ConvertTo-SecureString $Pass -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($User, $securePassword)
        
        # 连接VPN
        rasdial $connectionName $User $Pass
        Start-Sleep -Seconds 5
        
        # 检查连接状态
        $connection = Get-VpnConnection -Name $connectionName
        if ($connection.ConnectionStatus -eq "Connected") {
            Write-Host "✅ IKEv2 EAP连接成功！" -ForegroundColor Green
            
            # 测试IP地址
            Test-IPAddress
            
            # 断开连接
            rasdial $connectionName /disconnect
            Write-Host "🔄 已断开连接" -ForegroundColor Yellow
            
            return $true
        } else {
            Write-Host "❌ IKEv2 EAP连接失败" -ForegroundColor Red
            return $false
        }
        
    } catch {
        Write-Host "❌ IKEv2 EAP测试出错: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 函数：创建L2TP/IPSec连接
function New-L2TPConnection {
    param([string]$Server, [string]$User, [string]$Pass, [string]$SharedKey)
    
    Write-Host "`n=== 测试 L2TP/IPSec ===" -ForegroundColor Cyan
    
    $connectionName = "Test-L2TP-IPSec"
    
    try {
        # 删除已存在的连接
        Remove-VpnConnection -Name $connectionName -Force -ErrorAction SilentlyContinue
        
        # 创建L2TP连接
        Add-VpnConnection -Name $connectionName -ServerAddress $Server -TunnelType L2tp -L2tpPsk $SharedKey -AuthenticationMethod MSChapv2 -EncryptionLevel Optional -Force
        
        Write-Host "✅ L2TP/IPSec连接已创建" -ForegroundColor Green
        
        # 尝试连接
        Write-Host "🔄 尝试连接..." -ForegroundColor Yellow
        
        # 连接VPN
        rasdial $connectionName $User $Pass
        Start-Sleep -Seconds 5
        
        # 检查连接状态
        $connection = Get-VpnConnection -Name $connectionName
        if ($connection.ConnectionStatus -eq "Connected") {
            Write-Host "✅ L2TP/IPSec连接成功！" -ForegroundColor Green
            
            # 测试IP地址
            Test-IPAddress
            
            # 断开连接
            rasdial $connectionName /disconnect
            Write-Host "🔄 已断开连接" -ForegroundColor Yellow
            
            return $true
        } else {
            Write-Host "❌ L2TP/IPSec连接失败" -ForegroundColor Red
            return $false
        }
        
    } catch {
        Write-Host "❌ L2TP/IPSec测试出错: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 函数：测试IP地址
function Test-IPAddress {
    Write-Host "🔍 检查公网IP地址..." -ForegroundColor Yellow
    
    try {
        $ip = (Invoke-WebRequest -Uri "https://api.ipify.org" -UseBasicParsing).Content
        Write-Host "当前公网IP: $ip" -ForegroundColor Cyan
        
        if ($ip -eq $ServerIP) {
            Write-Host "✅ IP地址正确，VPN连接有效" -ForegroundColor Green
        } else {
            Write-Host "⚠️ IP地址不匹配，可能VPN未生效" -ForegroundColor Yellow
        }
        
        # 测试Google访问
        Write-Host "🔍 测试Google服务访问..." -ForegroundColor Yellow
        try {
            $response = Invoke-WebRequest -Uri "https://www.google.com" -UseBasicParsing -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ Google访问正常" -ForegroundColor Green
            }
        } catch {
            Write-Host "❌ Google访问失败" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "❌ 无法获取IP地址" -ForegroundColor Red
    }
}

# 函数：清理测试连接
function Remove-TestConnections {
    Write-Host "`n=== 清理测试连接 ===" -ForegroundColor Cyan
    
    $testConnections = @("Test-IKEv2-EAP", "Test-L2TP-IPSec")
    
    foreach ($conn in $testConnections) {
        try {
            Remove-VpnConnection -Name $conn -Force -ErrorAction SilentlyContinue
            Write-Host "🗑️ 已删除测试连接: $conn" -ForegroundColor Gray
        } catch {
            # 忽略错误
        }
    }
}

# 主测试流程
Write-Host "`n开始VPN连接测试..." -ForegroundColor Green

# 1. 测试端口连通性
Test-VPNPorts -Server $ServerIP

# 2. 测试IKEv2 EAP连接
$ikev2Result = New-IKEv2EAPConnection -Server $ServerIP -User $Username -Pass $Password

# 3. 测试L2TP/IPSec连接
$l2tpResult = New-L2TPConnection -Server $ServerIP -User $Username -Pass $Password -SharedKey $PSK

# 4. 清理测试连接
Remove-TestConnections

# 5. 总结结果
Write-Host "`n" + "="*50 -ForegroundColor Green
Write-Host "测试结果总结" -ForegroundColor Green
Write-Host "="*50 -ForegroundColor Green

Write-Host "`n📊 连接方式测试结果:" -ForegroundColor Cyan
if ($ikev2Result) {
    Write-Host "✅ IKEv2 + EAP认证: 成功" -ForegroundColor Green
} else {
    Write-Host "❌ IKEv2 + EAP认证: 失败" -ForegroundColor Red
}

if ($l2tpResult) {
    Write-Host "✅ L2TP/IPSec: 成功" -ForegroundColor Green
} else {
    Write-Host "❌ L2TP/IPSec: 失败" -ForegroundColor Red
}

Write-Host "`n📋 建议:" -ForegroundColor Yellow
if ($ikev2Result) {
    Write-Host "• 推荐使用 IKEv2 + EAP认证（用户名密码）" -ForegroundColor Green
} elseif ($l2tpResult) {
    Write-Host "• 推荐使用 L2TP/IPSec" -ForegroundColor Green
} else {
    Write-Host "• 所有连接方式都失败，请检查网络环境和服务器配置" -ForegroundColor Red
}

Write-Host "`n🔗 手动配置信息:" -ForegroundColor Cyan
Write-Host "服务器地址: $ServerIP"
Write-Host "用户名: $Username"
Write-Host "密码: $Password"
Write-Host "预共享密钥: $PSK"

Write-Host "`n测试完成！" -ForegroundColor Green
