# Windows VPN连接修复脚本
# 解决"IKE身份验证凭证不可接受"问题

Write-Host "🔧 修复Windows VPN连接问题..." -ForegroundColor Yellow

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInR<PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    pause
    exit 1
}

# 1. 修改注册表以支持L2TP/IPSec
Write-Host "1. 修改注册表配置..." -ForegroundColor Green

try {
    # 允许NAT穿透 - 关键设置
    New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\PolicyAgent" -Name "AssumeUDPEncapsulationContextOnSendRule" -Value 2 -PropertyType DWORD -Force | Out-Null

    # 禁用IPSec限制
    New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\RasMan\Parameters" -Name "ProhibitIpSec" -Value 0 -PropertyType DWORD -Force | Out-Null

    # L2TP关键注册表项 - 解决"处理错误"
    New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\RasMan\Parameters" -Name "AllowL2TPWeakCrypto" -Value 1 -PropertyType DWORD -Force | Out-Null

    # 强制UDP封装 - 解决NAT问题
    New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\PolicyAgent" -Name "AssumeUDPEncapsulationContextOnSendRule" -Value 2 -PropertyType DWORD -Force | Out-Null

    # 允许较弱的加密算法
    New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\PolicyAgent" -Name "NegotiateDH2048_AES256" -Value 1 -PropertyType DWORD -Force | Out-Null

    Write-Host "✅ 注册表配置完成" -ForegroundColor Green
} catch {
    Write-Host "❌ 注册表配置失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 配置Windows防火墙
Write-Host "2. 配置Windows防火墙..." -ForegroundColor Green
try {
    # 允许L2TP流量
    New-NetFirewallRule -DisplayName "L2TP-In" -Direction Inbound -Protocol UDP -LocalPort 1701 -Action Allow -ErrorAction SilentlyContinue | Out-Null
    New-NetFirewallRule -DisplayName "L2TP-Out" -Direction Outbound -Protocol UDP -LocalPort 1701 -Action Allow -ErrorAction SilentlyContinue | Out-Null

    # 允许IPSec流量
    New-NetFirewallRule -DisplayName "IPSec-IKE-In" -Direction Inbound -Protocol UDP -LocalPort 500 -Action Allow -ErrorAction SilentlyContinue | Out-Null
    New-NetFirewallRule -DisplayName "IPSec-IKE-Out" -Direction Outbound -Protocol UDP -LocalPort 500 -Action Allow -ErrorAction SilentlyContinue | Out-Null
    New-NetFirewallRule -DisplayName "IPSec-NAT-In" -Direction Inbound -Protocol UDP -LocalPort 4500 -Action Allow -ErrorAction SilentlyContinue | Out-Null
    New-NetFirewallRule -DisplayName "IPSec-NAT-Out" -Direction Outbound -Protocol UDP -LocalPort 4500 -Action Allow -ErrorAction SilentlyContinue | Out-Null

    # 允许ESP协议
    New-NetFirewallRule -DisplayName "IPSec-ESP" -Direction Inbound -Protocol 50 -Action Allow -ErrorAction SilentlyContinue | Out-Null

    Write-Host "✅ 防火墙规则配置完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️  防火墙配置警告（可能已存在规则）" -ForegroundColor Yellow
}

# 3. 删除现有VPN连接
Write-Host "3. 删除现有VPN连接..." -ForegroundColor Green
try {
    Remove-VpnConnection -Name "Google-VPN" -Force -ErrorAction SilentlyContinue
    Remove-VpnConnection -Name "Google-VPN-L2TP" -Force -ErrorAction SilentlyContinue
    Remove-VpnConnection -Name "Google-VPN-IKEv2" -Force -ErrorAction SilentlyContinue
    Write-Host "✅ 现有连接已删除" -ForegroundColor Green
} catch {
    Write-Host "⚠️  删除连接时出现警告（可忽略）" -ForegroundColor Yellow
}

# 4. 创建增强的L2TP/IPSec连接
Write-Host "4. 创建增强的L2TP/IPSec VPN连接..." -ForegroundColor Green
try {
    # 创建L2TP连接，使用最兼容的设置
    Add-VpnConnection -Name "Google-VPN-L2TP" -ServerAddress "************" -TunnelType L2tp -L2tpPsk "GoogleVPN2023!" -AuthenticationMethod MSChapv2 -EncryptionLevel Optional -Force

    # 设置连接属性以解决NAT问题
    Set-VpnConnection -Name "Google-VPN-L2TP" -SplitTunneling $False -RememberCredential $True -Force

    Write-Host "✅ L2TP/IPSec连接创建成功" -ForegroundColor Green
    Write-Host "连接名称: Google-VPN-L2TP" -ForegroundColor Cyan
} catch {
    Write-Host "❌ L2TP连接创建失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 创建IKEv2连接（备用）
Write-Host "5. 创建IKEv2 VPN连接..." -ForegroundColor Green
try {
    Add-VpnConnection -Name "Google-VPN-IKEv2" -ServerAddress "************" -TunnelType IKEv2 -AuthenticationMethod MSChapv2 -EncryptionLevel Optional -Force
    
    # 尝试配置IPSec参数
    try {
        Set-VpnConnectionIPsecConfiguration -ConnectionName "Google-VPN-IKEv2" -AuthenticationTransformConstants SHA196 -CipherTransformConstants AES128 -EncryptionMethod AES128 -IntegrityCheckMethod SHA1 -DHGroup Group2 -PfsGroup None -Force
        Write-Host "✅ IKEv2连接创建成功" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  IKEv2 IPSec配置警告（连接仍可用）" -ForegroundColor Yellow
    }
    
    Write-Host "连接名称: Google-VPN-IKEv2" -ForegroundColor Cyan
} catch {
    Write-Host "❌ IKEv2连接创建失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 显示连接信息
Write-Host "`n📋 VPN连接信息:" -ForegroundColor Cyan
Write-Host "服务器地址: ************" -ForegroundColor White
Write-Host "预共享密钥: GoogleVPN2023!" -ForegroundColor White
Write-Host "用户名: vpnuser" -ForegroundColor White
Write-Host "密码: GoogleVPN2023!" -ForegroundColor White

Write-Host "`n🔧 连接方法:" -ForegroundColor Cyan
Write-Host "方法1 (推荐): 使用 Google-VPN-L2TP 连接" -ForegroundColor Green
Write-Host "  - 到 设置 > 网络和Internet > VPN" -ForegroundColor White
Write-Host "  - 点击 'Google-VPN-L2TP' 连接" -ForegroundColor White
Write-Host "  - 输入用户名: vpnuser" -ForegroundColor White
Write-Host "  - 输入密码: GoogleVPN2023!" -ForegroundColor White

Write-Host "`n方法2 (备用): 使用 Google-VPN-IKEv2 连接" -ForegroundColor Yellow
Write-Host "  - 到 设置 > 网络和Internet > VPN" -ForegroundColor White
Write-Host "  - 点击 'Google-VPN-IKEv2' 连接" -ForegroundColor White
Write-Host "  - 输入用户名: vpnuser" -ForegroundColor White
Write-Host "  - 输入密码: GoogleVPN2023!" -ForegroundColor White

Write-Host "`n✅ 配置完成！" -ForegroundColor Green
Write-Host "⚠️  需要重启计算机使注册表更改生效" -ForegroundColor Yellow

$restart = Read-Host "`n是否现在重启计算机？(y/n)"
if ($restart -eq 'y' -or $restart -eq 'Y') {
    Write-Host "正在重启计算机..." -ForegroundColor Yellow
    Restart-Computer -Force
} else {
    Write-Host "请手动重启计算机后再尝试连接VPN" -ForegroundColor Yellow
}
