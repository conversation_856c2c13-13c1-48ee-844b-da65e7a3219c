# 简单的VPN连接测试
param(
    [string]$ServerIP = "*************"
)

Write-Host "=== 简单VPN连接测试 ===" -ForegroundColor Green
Write-Host "服务器: $ServerIP" -ForegroundColor Cyan

# 测试端口连通性
Write-Host "`n测试VPN端口连通性..." -ForegroundColor Yellow

$ports = @(22, 500, 4500, 1701)
foreach ($port in $ports) {
    Write-Host "测试端口 $port..." -NoNewline
    $result = Test-NetConnection -ComputerName $ServerIP -Port $port -WarningAction SilentlyContinue
    if ($result.TcpTestSucceeded) {
        Write-Host " 可达" -ForegroundColor Green
    } else {
        Write-Host " 不可达" -ForegroundColor Red
    }
}

# 获取当前IP
Write-Host "`n获取当前公网IP..." -ForegroundColor Yellow
try {
    $currentIP = (Invoke-WebRequest -Uri "https://api.ipify.org" -UseBasicParsing -TimeoutSec 10).Content
    Write-Host "当前IP: $currentIP" -ForegroundColor Cyan
} catch {
    Write-Host "无法获取当前IP" -ForegroundColor Red
}

# 显示VPN配置信息
Write-Host "`n=== VPN连接配置信息 ===" -ForegroundColor Green
Write-Host "服务器地址: $ServerIP" -ForegroundColor Cyan
Write-Host "VPN类型: IKEv2" -ForegroundColor Cyan
Write-Host "用户名: vpnuser" -ForegroundColor Cyan
Write-Host "密码: GoogleVPN2023!" -ForegroundColor Cyan
Write-Host "预共享密钥: GoogleVPN2023!" -ForegroundColor Cyan

Write-Host "`n=== 手动配置步骤 ===" -ForegroundColor Green
Write-Host "1. 打开 设置 -> 网络和Internet -> VPN" -ForegroundColor White
Write-Host "2. 点击 添加VPN连接" -ForegroundColor White
Write-Host "3. 选择 Windows (内置)" -ForegroundColor White
Write-Host "4. 连接名称: TestVPN" -ForegroundColor White
Write-Host "5. 服务器名称或地址: $ServerIP" -ForegroundColor White
Write-Host "6. VPN类型: IKEv2" -ForegroundColor White
Write-Host "7. 登录信息类型: 用户名和密码" -ForegroundColor White
Write-Host "8. 用户名: vpnuser" -ForegroundColor White
Write-Host "9. 密码: GoogleVPN2023!" -ForegroundColor White
Write-Host "10. 点击保存并连接" -ForegroundColor White

Write-Host "`n测试完成！" -ForegroundColor Green
