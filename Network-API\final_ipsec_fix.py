#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终IPSec修复 - 使用正确的libreswan语法
"""

import boto3
import json
import time

def send_command(ssm, instance_id, commands, description):
    """发送单个命令"""
    try:
        print(f"\n🔧 {description}")
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={'commands': commands},
            TimeoutSeconds=120
        )
        
        command_id = response['Command']['CommandId']
        
        # 等待命令完成
        for i in range(30):
            time.sleep(4)
            try:
                result = ssm.get_command_invocation(
                    CommandId=command_id,
                    InstanceId=instance_id
                )
                
                status = result['Status']
                if status == 'Success':
                    output = result['StandardOutputContent']
                    if output.strip():
                        print(f"✅ 成功: {output[-600:]}")
                    else:
                        print("✅ 成功")
                    return True
                elif status == 'Failed':
                    error = result['StandardErrorContent']
                    print(f"❌ 失败: {error}")
                    return False
                elif status in ['InProgress', 'Pending']:
                    continue
                    
            except Exception as e:
                if 'InvocationDoesNotExist' in str(e):
                    continue
                else:
                    print(f"❌ 错误: {e}")
                    return False
        
        print("⚠️ 超时")
        return False
        
    except Exception as e:
        print(f"❌ 发送命令失败: {e}")
        return False

def main():
    try:
        # 读取部署信息
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        instance_id = deployment_info['instance_id']
        region = deployment_info['region']
        
        print(f"实例ID: {instance_id}")
        
        # 创建SSM客户端
        ssm = boto3.client('ssm', region_name=region)
        
        # 步骤1: 停止服务并清理
        send_command(ssm, instance_id, [
            'systemctl stop ipsec',
            'rm -f /etc/ipsec.conf /etc/ipsec.secrets'
        ], "停止服务并清理配置")
        
        # 步骤2: 创建正确的libreswan配置
        send_command(ssm, instance_id, [
            'TOKEN=$(curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")',
            'PUBLIC_IP=$(curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/public-ipv4)',
            'cat > /etc/ipsec.conf << EOF',
            'version 2.0',
            '',
            'config setup',
            '    nat_traversal=yes',
            '    virtual_private=%v4:10.0.0.0/8,%v4:***********/16,%v4:**********/12',
            '    oe=off',
            '    protostack=netkey',
            '    nhelpers=0',
            '    interfaces=%defaultroute',
            '',
            'conn vpn-psk',
            '    auto=add',
            '    compress=no',
            '    type=tunnel',
            '    ikev2=insist',
            '    fragmentation=yes',
            '    forceencaps=yes',
            '    dpdaction=clear',
            '    dpddelay=300s',
            '    rekey=no',
            '    left=%defaultroute',
            '    leftid=$PUBLIC_IP',
            '    leftauth=psk',
            '    leftsubnet=0.0.0.0/0',
            '    right=%any',
            '    rightid=%any',
            '    rightauth=psk',
            '    rightsourceip=**********/24',
            '    rightdns=*******,*******',
            'EOF'
        ], "创建libreswan配置")
        
        # 步骤3: 创建密钥文件
        send_command(ssm, instance_id, [
            'TOKEN=$(curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")',
            'PUBLIC_IP=$(curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/public-ipv4)',
            'cat > /etc/ipsec.secrets << EOF',
            ': PSK "GoogleVPN2023!"',
            '$PUBLIC_IP %any : PSK "GoogleVPN2023!"',
            'EOF',
            'chmod 600 /etc/ipsec.secrets'
        ], "创建密钥文件")
        
        # 步骤4: 验证配置
        send_command(ssm, instance_id, [
            'ipsec addconn --config /etc/ipsec.conf --checkconfig'
        ], "验证配置")
        
        # 步骤5: 启动服务
        send_command(ssm, instance_id, [
            'systemctl start ipsec',
            'sleep 5',
            'systemctl status ipsec --no-pager'
        ], "启动IPSec服务")
        
        # 步骤6: 检查端口监听
        send_command(ssm, instance_id, [
            'ss -unp | grep -E ":(500|4500)"',
            'ipsec status'
        ], "检查端口和状态")
        
        print("\n✅ IPSec最终修复完成！")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    print("=== IPSec最终修复工具 ===")
    main()
