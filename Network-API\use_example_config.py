#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用libreswan示例配置
"""

import boto3
import json
import time

def send_command(ssm, instance_id, commands, description):
    """发送单个命令"""
    try:
        print(f"\n🔧 {description}")
        response = ssm.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={'commands': commands},
            TimeoutSeconds=120
        )
        
        command_id = response['Command']['CommandId']
        
        # 等待命令完成
        for i in range(30):
            time.sleep(4)
            try:
                result = ssm.get_command_invocation(
                    CommandId=command_id,
                    InstanceId=instance_id
                )
                
                status = result['Status']
                if status == 'Success':
                    output = result['StandardOutputContent']
                    if output.strip():
                        print(f"✅ 成功: {output[-800:]}")
                    else:
                        print("✅ 成功")
                    return True
                elif status == 'Failed':
                    error = result['StandardErrorContent']
                    print(f"❌ 失败: {error}")
                    return False
                elif status in ['InProgress', 'Pending']:
                    continue
                    
            except Exception as e:
                if 'InvocationDoesNotExist' in str(e):
                    continue
                else:
                    print(f"❌ 错误: {e}")
                    return False
        
        print("⚠️ 超时")
        return False
        
    except Exception as e:
        print(f"❌ 发送命令失败: {e}")
        return False

def main():
    try:
        # 读取部署信息
        with open('vpn_deployment_info.json', 'r') as f:
            deployment_info = json.load(f)
        
        instance_id = deployment_info['instance_id']
        region = deployment_info['region']
        
        print(f"实例ID: {instance_id}")
        
        # 创建SSM客户端
        ssm = boto3.client('ssm', region_name=region)
        
        # 查看示例配置
        send_command(ssm, instance_id, [
            'cat /usr/share/doc/libreswan/examples/l2tp-psk.conf'
        ], "查看L2TP PSK示例配置")
        
        # 停止服务并清理
        send_command(ssm, instance_id, [
            'systemctl stop ipsec',
            'rm -f /etc/ipsec.conf /etc/ipsec.secrets'
        ], "停止服务并清理")
        
        # 基于示例创建配置
        send_command(ssm, instance_id, [
            'TOKEN=$(curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")',
            'PUBLIC_IP=$(curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/public-ipv4)',
            'cp /usr/share/doc/libreswan/examples/l2tp-psk.conf /etc/ipsec.conf',
            'sed -i "s/**********/$PUBLIC_IP/g" /etc/ipsec.conf',
            'sed -i "s/*********/$PUBLIC_IP/g" /etc/ipsec.conf'
        ], "基于示例创建配置")
        
        # 创建密钥文件
        send_command(ssm, instance_id, [
            'TOKEN=$(curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")',
            'PUBLIC_IP=$(curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/public-ipv4)',
            'cat > /etc/ipsec.secrets << EOF',
            ': PSK "GoogleVPN2023!"',
            '$PUBLIC_IP %any : PSK "GoogleVPN2023!"',
            'EOF',
            'chmod 600 /etc/ipsec.secrets'
        ], "创建密钥文件")
        
        # 查看最终配置
        send_command(ssm, instance_id, [
            'cat /etc/ipsec.conf'
        ], "查看最终配置")
        
        # 验证配置
        send_command(ssm, instance_id, [
            'ipsec addconn --config /etc/ipsec.conf --checkconfig'
        ], "验证配置")
        
        # 启动服务
        send_command(ssm, instance_id, [
            'systemctl start ipsec',
            'sleep 5',
            'systemctl status ipsec --no-pager'
        ], "启动IPSec服务")
        
        # 检查端口
        send_command(ssm, instance_id, [
            'ss -unp | grep -E ":(500|4500)"',
            'ipsec status'
        ], "检查端口和状态")
        
        print("\n✅ 使用示例配置完成！")
        
    except Exception as e:
        print(f"❌ 配置失败: {e}")

if __name__ == "__main__":
    print("=== 使用libreswan示例配置 ===")
    main()
